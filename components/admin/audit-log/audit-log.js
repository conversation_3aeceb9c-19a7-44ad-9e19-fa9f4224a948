/**
 * 操作日志和审计功能组件
 * Audit Log Component
 * 
 * 基于57个权限常量的完整审计日志系统
 * 记录所有权限相关的操作和数据变更
 */

const { getCurrentUserPermissions, PERMISSIONS, hasPermission } = require('../../../utils/role-permission');

Component({
  properties: {
    // 显示模式：timeline(时间线) | table(表格) | chart(图表)
    displayMode: {
      type: String,
      value: 'timeline'
    },
    
    // 是否显示详细信息
    showDetails: {
      type: Boolean,
      value: true
    },
    
    // 筛选的用户ID
    userId: {
      type: String,
      value: ''
    },
    
    // 筛选的操作类型
    actionType: {
      type: String,
      value: ''
    }
  },

  data: {
    loading: false,
    userPermissions: null,
    
    // 审计日志列表
    auditLogs: [],
    
    // 筛选条件
    filters: {
      dateRange: 'week', // today, week, month, quarter, year, custom
      startDate: '',
      endDate: '',
      userId: '',
      actionType: '',
      permissionType: '', // platform, tenant, common
      result: '', // success, failed, all
      keyword: ''
    },
    
    // 统计数据
    statistics: {
      total_operations: 0,
      success_operations: 0,
      failed_operations: 0,
      unique_users: 0,
      most_active_user: '',
      most_common_action: '',
      risk_operations: 0
    },
    
    // 操作类型选项
    actionTypes: [
      { value: 'create', label: '创建操作', icon: '➕', color: '#52c41a' },
      { value: 'update', label: '更新操作', icon: '✏️', color: '#1890ff' },
      { value: 'delete', label: '删除操作', icon: '🗑️', color: '#f5222d' },
      { value: 'view', label: '查看操作', icon: '👁️', color: '#666' },
      { value: 'approve', label: '审批操作', icon: '✅', color: '#722ed1' },
      { value: 'export', label: '导出操作', icon: '📤', color: '#fa8c16' },
      { value: 'import', label: '导入操作', icon: '📥', color: '#13c2c2' },
      { value: 'login', label: '登录操作', icon: '🔐', color: '#eb2f96' },
      { value: 'logout', label: '登出操作', icon: '🚪', color: '#8c8c8c' },
      { value: 'permission', label: '权限操作', icon: '🔑', color: '#faad14' }
    ],
    
    // 权限类型选项
    permissionTypes: [
      { value: 'platform', label: '平台级权限', count: 15, color: '#667eea' },
      { value: 'tenant', label: '租户级权限', count: 28, color: '#28a745' },
      { value: 'common', label: '通用权限', count: 14, color: '#ffc107' }
    ],
    
    // 风险等级选项
    riskLevels: [
      { value: 'low', label: '低风险', color: '#52c41a' },
      { value: 'medium', label: '中风险', color: '#faad14' },
      { value: 'high', label: '高风险', color: '#f5222d' },
      { value: 'critical', label: '严重风险', color: '#722ed1' }
    ],
    
    // 当前查看的日志详情
    currentLogDetail: null,
    showLogDetail: false,
    
    // 图表数据
    chartData: {
      operationTrend: [],
      actionDistribution: [],
      userActivity: [],
      permissionUsage: []
    }
  },

  lifetimes: {
    attached() {
      this.initializeAuditLog();
    }
  },

  observers: {
    'filters.**': function() {
      this.loadAuditLogs();
    }
  },

  methods: {
    /**
     * 初始化审计日志
     */
    async initializeAuditLog() {
      try {
        this.setData({ loading: true });
        
        // 获取用户权限
        const userPermissions = await getCurrentUserPermissions();
        
        // 检查审计日志查看权限
        if (!hasPermission(userPermissions.role, PERMISSIONS.PLATFORM_SYSTEM_MONITOR)) {
          console.warn('用户没有审计日志查看权限');
          return;
        }
        
        this.setData({
          userPermissions
        });
        
        // 加载审计日志数据
        await this.loadAuditLogs();
        await this.loadStatistics();
        
      } catch (error) {
        console.error('初始化审计日志失败:', error);
      } finally {
        this.setData({ loading: false });
      }
    },

    /**
     * 加载审计日志数据
     */
    async loadAuditLogs() {
      try {
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',
          data: {
            action: 'getOperationLogs',
            data: {
              filters: this.data.filters,
              page: 1,
              limit: 100
            }
          }
        });
        
        if (result.result.success) {
          const auditLogs = result.result.data.map(log => ({
            ...log,
            actionTypeInfo: this.getActionTypeInfo(log.action_type),
            permissionTypeInfo: this.getPermissionTypeInfo(log.permission_type),
            riskLevel: this.calculateRiskLevel(log),
            formattedTime: this.formatTime(log.timestamp)
          }));
          
          this.setData({
            auditLogs
          });
        }
        
      } catch (error) {
        console.error('加载审计日志失败:', error);
      }
    },

    /**
     * 加载统计数据
     */
    async loadStatistics() {
      try {
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',
          data: {
            action: 'getAuditStatistics',
            data: {
              filters: this.data.filters
            }
          }
        });
        
        if (result.result.success) {
          this.setData({
            statistics: result.result.data.statistics,
            chartData: result.result.data.charts
          });
        }
        
      } catch (error) {
        console.error('加载统计数据失败:', error);
      }
    },

    /**
     * 计算操作风险等级
     */
    calculateRiskLevel(log) {
      // 基于操作类型、权限类型、结果等因素计算风险等级
      let riskScore = 0;
      
      // 操作类型风险评分
      const actionRiskScores = {
        'delete': 3,
        'permission': 3,
        'approve': 2,
        'export': 2,
        'update': 1,
        'create': 1,
        'view': 0,
        'login': 0,
        'logout': 0
      };
      
      riskScore += actionRiskScores[log.action_type] || 0;
      
      // 权限类型风险评分
      if (log.permission_type === 'platform') {
        riskScore += 2;
      } else if (log.permission_type === 'tenant') {
        riskScore += 1;
      }
      
      // 操作结果风险评分
      if (log.result === 'failed') {
        riskScore += 1;
      }
      
      // 异常时间操作风险评分
      const hour = new Date(log.timestamp).getHours();
      if (hour < 6 || hour > 22) {
        riskScore += 1;
      }
      
      // 返回风险等级
      if (riskScore >= 5) return 'critical';
      if (riskScore >= 4) return 'high';
      if (riskScore >= 2) return 'medium';
      return 'low';
    },

    /**
     * 获取操作类型信息
     */
    getActionTypeInfo(actionType) {
      return this.data.actionTypes.find(type => type.value === actionType) || 
             { value: actionType, label: actionType, icon: '❓', color: '#8c8c8c' };
    },

    /**
     * 获取权限类型信息
     */
    getPermissionTypeInfo(permissionType) {
      return this.data.permissionTypes.find(type => type.value === permissionType) || 
             { value: permissionType, label: permissionType, count: 0, color: '#8c8c8c' };
    },

    /**
     * 获取风险等级信息
     */
    getRiskLevelInfo(riskLevel) {
      return this.data.riskLevels.find(level => level.value === riskLevel) || 
             { value: riskLevel, label: riskLevel, color: '#8c8c8c' };
    },

    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      const now = new Date();
      const time = new Date(timestamp);
      const diff = now - time;
      
      if (diff < 60000) return '刚刚';
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
      if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`;
      
      return time.toLocaleDateString() + ' ' + time.toLocaleTimeString();
    },

    /**
     * 筛选条件变更
     */
    onFilterChange(e) {
      const { field } = e.currentTarget.dataset;
      const { value } = e.detail;
      
      this.setData({
        [`filters.${field}`]: value
      });
    },

    /**
     * 日期范围变更
     */
    onDateRangeChange(e) {
      const { value } = e.detail;
      const dateRange = this.data.filters.dateRange;
      
      if (dateRange === 'custom') {
        const { field } = e.currentTarget.dataset;
        this.setData({
          [`filters.${field}`]: value
        });
      } else {
        this.setData({
          'filters.dateRange': value
        });
        
        // 自动设置日期范围
        this.setAutoDateRange(value);
      }
    },

    /**
     * 自动设置日期范围
     */
    setAutoDateRange(range) {
      const now = new Date();
      let startDate, endDate;
      
      switch (range) {
        case 'today':
          startDate = endDate = now.toISOString().split('T')[0];
          break;
        case 'week':
          startDate = new Date(now - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
          endDate = now.toISOString().split('T')[0];
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
          endDate = now.toISOString().split('T')[0];
          break;
        case 'quarter':
          const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
          startDate = quarterStart.toISOString().split('T')[0];
          endDate = now.toISOString().split('T')[0];
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0];
          endDate = now.toISOString().split('T')[0];
          break;
      }
      
      if (startDate && endDate) {
        this.setData({
          'filters.startDate': startDate,
          'filters.endDate': endDate
        });
      }
    },

    /**
     * 搜索关键词输入
     */
    onSearchInput(e) {
      const { value } = e.detail;
      
      this.setData({
        'filters.keyword': value
      });
      
      // 延迟搜索
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.loadAuditLogs();
      }, 500);
    },

    /**
     * 显示日志详情
     */
    showLogDetail(e) {
      const { log } = e.currentTarget.dataset;
      
      this.setData({
        currentLogDetail: log,
        showLogDetail: true
      });
    },

    /**
     * 关闭日志详情
     */
    closeLogDetail() {
      this.setData({
        showLogDetail: false,
        currentLogDetail: null
      });
    },

    /**
     * 导出审计日志
     */
    async exportAuditLogs() {
      try {
        // 检查导出权限
        if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.DATA_EXPORT)) {
          wx.showToast({
            title: '权限不足',
            icon: 'error'
          });
          return;
        }
        
        wx.showLoading({ title: '正在导出...' });
        
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',
          data: {
            action: 'exportAuditLogs',
            data: {
              filters: this.data.filters,
              format: 'excel'
            }
          }
        });
        
        if (result.result.success) {
          // 下载文件
          wx.downloadFile({
            url: result.result.data.download_url,
            success: (res) => {
              wx.openDocument({
                filePath: res.tempFilePath,
                success: () => {
                  wx.showToast({
                    title: '导出成功',
                    icon: 'success'
                  });
                }
              });
            }
          });
        } else {
          wx.showToast({
            title: result.result.error || '导出失败',
            icon: 'error'
          });
        }
        
      } catch (error) {
        console.error('导出审计日志失败:', error);
        wx.showToast({
          title: '导出失败',
          icon: 'error'
        });
      } finally {
        wx.hideLoading();
      }
    },

    /**
     * 刷新数据
     */
    async refreshData() {
      this.setData({ loading: true });
      
      try {
        await Promise.all([
          this.loadAuditLogs(),
          this.loadStatistics()
        ]);
        
        wx.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      } catch (error) {
        wx.showToast({
          title: '刷新失败',
          icon: 'error'
        });
      } finally {
        this.setData({ loading: false });
      }
    },

    /**
     * 切换显示模式
     */
    switchDisplayMode(e) {
      const { mode } = e.currentTarget.dataset;
      this.setData({
        displayMode: mode
      });
      
      // 触发显示模式变更事件
      this.triggerEvent('displayModeChange', { mode });
    }
  }
});
