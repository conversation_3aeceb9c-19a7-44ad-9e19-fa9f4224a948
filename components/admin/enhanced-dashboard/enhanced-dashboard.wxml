/**
 * 增强系统仪表板组件
 * Enhanced System Dashboard Component
 * 
 * 基于Context7最佳实践的完整管理后台仪表板
 * 支持实时数据监控、图表展示、告警管理等核心功能
 */

<template>
  <view class="enhanced-dashboard">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载仪表板数据...</text>
    </view>

    <!-- 仪表板内容 -->
    <view wx:else class="dashboard-content">
      
      <!-- 顶部统计卡片 -->
      <view class="stats-cards">
        <view class="stats-row">
          <view class="stat-card" bindtap="onStatCardTap" data-type="users">
            <view class="stat-icon users-icon">👥</view>
            <view class="stat-content">
              <view class="stat-number">{{formatNumber(stats.totalUsers)}}</view>
              <view class="stat-label">总用户数</view>
              <view class="stat-change {{stats.userGrowth >= 0 ? 'positive' : 'negative'}}">
                {{stats.userGrowth >= 0 ? '+' : ''}}{{stats.userGrowth}}%
              </view>
            </view>
          </view>
          
          <view class="stat-card" bindtap="onStatCardTap" data-type="tenants">
            <view class="stat-icon tenants-icon">🏢</view>
            <view class="stat-content">
              <view class="stat-number">{{formatNumber(stats.totalTenants)}}</view>
              <view class="stat-label">租户数量</view>
              <view class="stat-change {{stats.tenantGrowth >= 0 ? 'positive' : 'negative'}}">
                {{stats.tenantGrowth >= 0 ? '+' : ''}}{{stats.tenantGrowth}}%
              </view>
            </view>
          </view>
        </view>
        
        <view class="stats-row">
          <view class="stat-card" bindtap="onStatCardTap" data-type="flocks">
            <view class="stat-icon flocks-icon">🦆</view>
            <view class="stat-content">
              <view class="stat-number">{{formatNumber(stats.activeFlocks)}}</view>
              <view class="stat-label">活跃鹅群</view>
              <view class="stat-change positive">
                +{{stats.newFlocks}}今日新增
              </view>
            </view>
          </view>
          
          <view class="stat-card" bindtap="onStatCardTap" data-type="revenue">
            <view class="stat-icon revenue-icon">💰</view>
            <view class="stat-content">
              <view class="stat-number">¥{{formatNumber(stats.monthlyRevenue)}}</view>
              <view class="stat-label">月度收入</view>
              <view class="stat-change {{stats.revenueGrowth >= 0 ? 'positive' : 'negative'}}">
                {{stats.revenueGrowth >= 0 ? '+' : ''}}{{stats.revenueGrowth}}%
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 系统健康状态 -->
      <view class="system-health-section">
        <view class="section-header">
          <view class="section-title">系统健康状态</view>
          <view class="health-status {{systemHealth.overall}}">
            <view class="health-icon">{{getHealthIcon(systemHealth.overall)}}</view>
            <text class="health-text">{{getHealthText(systemHealth.overall)}}</text>
          </view>
        </view>
        
        <view class="health-components">
          <view class="health-item" wx:for="{{healthComponents}}" wx:key="name">
            <view class="component-name">{{item.name}}</view>
            <view class="component-status {{item.status}}">
              <view class="status-dot"></view>
              <text>{{item.statusText}}</text>
            </view>
            <view class="component-metric">{{item.metric}}</view>
          </view>
        </view>
      </view>

      <!-- 实时监控图表 -->
      <view class="monitoring-charts">
        <view class="section-header">
          <view class="section-title">实时监控</view>
          <view class="chart-controls">
            <view class="time-selector">
              <view 
                class="time-option {{timeRange === '1h' ? 'active' : ''}}" 
                bindtap="setTimeRange" 
                data-range="1h"
              >1小时</view>
              <view 
                class="time-option {{timeRange === '6h' ? 'active' : ''}}" 
                bindtap="setTimeRange" 
                data-range="6h"
              >6小时</view>
              <view 
                class="time-option {{timeRange === '24h' ? 'active' : ''}}" 
                bindtap="setTimeRange" 
                data-range="24h"
              >24小时</view>
            </view>
          </view>
        </view>
        
        <view class="charts-container">
          <!-- 用户活动图表 -->
          <view class="chart-card">
            <view class="chart-title">用户活动趋势</view>
            <canvas 
              class="chart-canvas" 
              canvas-id="userActivityChart"
              disable-scroll="true"
            ></canvas>
          </view>
          
          <!-- 系统性能图表 -->
          <view class="chart-card">
            <view class="chart-title">系统性能指标</view>
            <canvas 
              class="chart-canvas" 
              canvas-id="performanceChart"
              disable-scroll="true"
            ></canvas>
          </view>
        </view>
      </view>

      <!-- 系统告警 -->
      <view class="alerts-section" wx:if="{{alerts.length > 0}}">
        <view class="section-header">
          <view class="section-title">系统告警</view>
          <view class="alerts-count {{alerts.length > 0 ? 'has-alerts' : ''}}">
            {{alerts.length}}条
          </view>
        </view>
        
        <view class="alerts-list">
          <view class="alert-item {{alert.severity}}" wx:for="{{alerts}}" wx:key="_id">
            <view class="alert-icon">{{getAlertIcon(alert.severity)}}</view>
            <view class="alert-content">
              <view class="alert-title">{{alert.title}}</view>
              <view class="alert-message">{{alert.message}}</view>
              <view class="alert-time">{{formatTime(alert.created_time)}}</view>
            </view>
            <view class="alert-actions">
              <view class="alert-action" bindtap="handleAlert" data-alert="{{alert}}">
                处理
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 快速操作 -->
      <view class="quick-actions">
        <view class="section-header">
          <view class="section-title">快速操作</view>
        </view>
        
        <view class="actions-grid">
          <view 
            class="action-item" 
            wx:for="{{quickActions}}" 
            wx:key="id"
            bindtap="executeQuickAction"
            data-action="{{item}}"
          >
            <view class="action-icon">{{item.icon}}</view>
            <view class="action-title">{{item.title}}</view>
          </view>
        </view>
      </view>

      <!-- 最近活动 -->
      <view class="recent-activities">
        <view class="section-header">
          <view class="section-title">最近活动</view>
          <view class="view-all" bindtap="viewAllActivities">查看全部</view>
        </view>
        
        <view class="activities-list">
          <view class="activity-item" wx:for="{{recentActivities}}" wx:key="_id">
            <view class="activity-user">
              <image class="user-avatar" src="{{item.user_avatar || '/images/default-avatar.png'}}" />
            </view>
            <view class="activity-content">
              <view class="activity-text">
                <text class="user-name">{{item.user_name}}</text>
                <text class="activity-action">{{item.description}}</text>
              </view>
              <view class="activity-time">{{formatTime(item.created_time)}}</view>
            </view>
          </view>
        </view>
      </view>

    </view>

    <!-- 自动刷新控制 -->
    <view class="refresh-controls" wx:if="{{!loading}}">
      <view class="refresh-toggle" bindtap="toggleAutoRefresh">
        <view class="toggle-icon {{autoRefresh ? 'active' : ''}}">🔄</view>
        <text class="toggle-text">自动刷新{{autoRefresh ? '(开启)' : '(关闭)'}}</text>
      </view>
      <view class="last-update">
        最后更新: {{formatTime(lastUpdated)}}
      </view>
    </view>

  </view>
</template>