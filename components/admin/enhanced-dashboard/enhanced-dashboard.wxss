/**
 * 增强系统仪表板组件样式
 * Enhanced System Dashboard Component Styles
 * 
 * 基于微信小程序设计规范的现代化仪表板样式
 * 支持响应式布局、深色主题、动画效果
 */

.enhanced-dashboard {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: #666;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e6e6e6;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 仪表板内容 */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 统计卡片 */
.stats-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.stats-row {
  display: flex;
  gap: 16rpx;
}

.stat-card {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.15);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  pointer-events: none;
}

.stat-card:nth-child(2) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 4rpx 20rpx rgba(240, 147, 251, 0.15);
}

.stat-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
  opacity: 0.9;
}

.stat-content {
  flex: 1;
  color: white;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.stat-change {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  background-color: rgba(255, 255, 255, 0.2);
  display: inline-block;
}

.stat-change.positive {
  background-color: rgba(82, 196, 26, 0.2);
}

.stat-change.negative {
  background-color: rgba(245, 34, 45, 0.2);
}

/* 系统健康状态 */
.system-health-section {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.health-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.health-status.good {
  background-color: #f6ffed;
  color: #52c41a;
}

.health-status.warning {
  background-color: #fffbe6;
  color: #faad14;
}

.health-status.error {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.health-icon {
  font-size: 20rpx;
}

.health-components {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.health-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.health-item:last-child {
  border-bottom: none;
}

.component-name {
  font-size: 28rpx;
  color: #262626;
  flex: 1;
}

.component-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  flex: 1;
  justify-content: center;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #52c41a;
}

.component-status.warning .status-dot {
  background-color: #faad14;
}

.component-status.error .status-dot,
.component-status.unhealthy .status-dot {
  background-color: #ff4d4f;
}

.component-metric {
  font-size: 24rpx;
  color: #8c8c8c;
  flex: 1;
  text-align: right;
}

/* 监控图表 */
.monitoring-charts {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
}

.chart-controls {
  display: flex;
  align-items: center;
}

.time-selector {
  display: flex;
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 4rpx;
}

.time-option {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  color: #666;
  border-radius: 4rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-option.active {
  background: #1890ff;
  color: white;
}

.charts-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-top: 20rpx;
}

.chart-card {
  background: #fafafa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.chart-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16rpx;
}

.chart-canvas {
  width: 100%;
  height: 300rpx;
  border-radius: 8rpx;
}

/* 系统告警 */
.alerts-section {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
}

.alerts-count {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  background-color: #f0f0f0;
  color: #8c8c8c;
}

.alerts-count.has-alerts {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-top: 16rpx;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  padding: 16rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid #1890ff;
  background: #f6f6f6;
}

.alert-item.warning {
  border-left-color: #faad14;
  background: #fffbe6;
}

.alert-item.error,
.alert-item.critical {
  border-left-color: #ff4d4f;
  background: #fff2f0;
}

.alert-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8rpx;
}

.alert-message {
  font-size: 24rpx;
  color: #595959;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.alert-time {
  font-size: 20rpx;
  color: #8c8c8c;
}

.alert-actions {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-left: 16rpx;
}

.alert-action {
  padding: 8rpx 16rpx;
  background: #1890ff;
  color: white;
  border-radius: 6rpx;
  font-size: 20rpx;
  text-align: center;
  cursor: pointer;
}

/* 快速操作 */
.quick-actions {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-top: 16rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.action-item:active {
  transform: scale(0.95);
}

.action-item:nth-child(2n) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.action-item:nth-child(3n) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.action-icon {
  font-size: 40rpx;
  margin-bottom: 12rpx;
}

.action-title {
  font-size: 24rpx;
  text-align: center;
  font-weight: 500;
}

/* 最近活动 */
.recent-activities {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
}

.view-all {
  font-size: 24rpx;
  color: #1890ff;
  cursor: pointer;
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-top: 16rpx;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-user {
  margin-right: 16rpx;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #f5f5f5;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 26rpx;
  color: #262626;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.user-name {
  font-weight: 600;
  color: #1890ff;
}

.activity-action {
  color: #595959;
}

.activity-time {
  font-size: 20rpx;
  color: #8c8c8c;
}

/* 刷新控制 */
.refresh-controls {
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.refresh-toggle {
  display: flex;
  align-items: center;
  gap: 12rpx;
  cursor: pointer;
}

.toggle-icon {
  font-size: 32rpx;
  color: #8c8c8c;
  transition: color 0.3s ease;
}

.toggle-icon.active {
  color: #1890ff;
  animation: spin 2s linear infinite;
}

.toggle-text {
  font-size: 26rpx;
  color: #595959;
}

.last-update {
  font-size: 24rpx;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .stats-row {
    flex-direction: column;
  }
  
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}