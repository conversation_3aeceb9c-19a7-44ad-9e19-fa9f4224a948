/**
 * 系统监控仪表板组件
 * System Monitoring Dashboard Component
 * 
 * 基于57个权限的系统健康监控和性能分析
 * 提供实时系统状态、权限使用统计、用户活动监控等功能
 */

const { getCurrentUserPermissions, PERMISSIONS, hasPermission } = require('../../../utils/role-permission');

Component({
  properties: {
    // 刷新间隔（秒）
    refreshInterval: {
      type: Number,
      value: 30
    },
    
    // 是否自动刷新
    autoRefresh: {
      type: Boolean,
      value: true
    },
    
    // 显示的监控项目
    monitorItems: {
      type: Array,
      value: ['system', 'permissions', 'users', 'performance']
    }
  },

  data: {
    loading: false,
    userPermissions: null,
    
    // 系统健康状态
    systemHealth: {
      overall: 'good', // good, warning, error
      database: 'healthy',
      cloudFunctions: 'healthy',
      storage: 'healthy',
      network: 'healthy',
      lastCheck: ''
    },
    
    // 实时统计数据
    realTimeStats: {
      // 用户统计
      totalUsers: 0,
      activeUsers: 0,
      onlineUsers: 0,
      newUsersToday: 0,
      
      // 权限统计
      totalPermissions: 57,
      platformPermissions: 15,
      tenantPermissions: 28,
      commonPermissions: 14,
      
      // 操作统计
      totalOperations: 0,
      operationsToday: 0,
      successRate: 0,
      errorRate: 0,
      
      // 数据统计
      totalFlocks: 0,
      totalFinanceRecords: 0,
      totalMaterials: 0,
      totalHealthRecords: 0
    },
    
    // 性能指标
    performanceMetrics: {
      responseTime: 0,
      throughput: 0,
      errorRate: 0,
      cpuUsage: 0,
      memoryUsage: 0,
      diskUsage: 0
    },
    
    // 权限使用统计
    permissionUsage: {
      mostUsedPermissions: [],
      leastUsedPermissions: [],
      permissionsByCategory: {
        platform: { used: 0, total: 15 },
        tenant: { used: 0, total: 28 },
        common: { used: 0, total: 14 }
      }
    },
    
    // 用户活动统计
    userActivity: {
      activeUsersTrend: [],
      loginTrend: [],
      operationTrend: [],
      topActiveUsers: []
    },
    
    // 系统警告和通知
    systemAlerts: [],
    
    // 监控图表数据
    chartData: {
      systemHealthTrend: [],
      permissionUsageTrend: [],
      userActivityTrend: [],
      performanceTrend: []
    },
    
    // 自动刷新定时器
    refreshTimer: null,
    
    // 当前显示的详情
    showDetailModal: false,
    currentDetail: null,
    detailType: '' // system, permissions, users, performance
  },

  lifetimes: {
    attached() {
      this.initializeDashboard();
    },
    
    detached() {
      this.clearRefreshTimer();
    }
  },

  observers: {
    'autoRefresh': function(autoRefresh) {
      if (autoRefresh) {
        this.startAutoRefresh();
      } else {
        this.clearRefreshTimer();
      }
    }
  },

  methods: {
    /**
     * 初始化仪表板
     */
    async initializeDashboard() {
      try {
        this.setData({ loading: true });
        
        // 获取用户权限
        const userPermissions = await getCurrentUserPermissions();
        
        // 检查系统监控权限
        if (!hasPermission(userPermissions.role, PERMISSIONS.PLATFORM_SYSTEM_MONITOR)) {
          console.warn('用户没有系统监控权限');
          return;
        }
        
        this.setData({
          userPermissions
        });
        
        // 加载所有监控数据
        await this.loadAllMonitoringData();
        
        // 启动自动刷新
        if (this.data.autoRefresh) {
          this.startAutoRefresh();
        }
        
      } catch (error) {
        console.error('初始化仪表板失败:', error);
      } finally {
        this.setData({ loading: false });
      }
    },

    /**
     * 加载所有监控数据
     */
    async loadAllMonitoringData() {
      try {
        const promises = [];
        
        // 根据监控项目加载对应数据
        if (this.data.monitorItems.includes('system')) {
          promises.push(this.loadSystemHealth());
        }
        
        if (this.data.monitorItems.includes('permissions')) {
          promises.push(this.loadPermissionUsage());
        }
        
        if (this.data.monitorItems.includes('users')) {
          promises.push(this.loadUserActivity());
        }
        
        if (this.data.monitorItems.includes('performance')) {
          promises.push(this.loadPerformanceMetrics());
        }
        
        // 并行加载所有数据
        await Promise.all([
          ...promises,
          this.loadRealTimeStats(),
          this.loadSystemAlerts(),
          this.loadChartData()
        ]);
        
      } catch (error) {
        console.error('加载监控数据失败:', error);
      }
    },

    /**
     * 加载系统健康状态
     */
    async loadSystemHealth() {
      try {
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',
          data: {
            action: 'getSystemHealth'
          }
        });
        
        if (result.result.success) {
          this.setData({
            systemHealth: {
              ...result.result.data,
              lastCheck: new Date().toLocaleString()
            }
          });
        }
        
      } catch (error) {
        console.error('加载系统健康状态失败:', error);
        this.setData({
          'systemHealth.overall': 'error',
          'systemHealth.lastCheck': new Date().toLocaleString()
        });
      }
    },

    /**
     * 加载实时统计数据
     */
    async loadRealTimeStats() {
      try {
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',
          data: {
            action: 'getRealTimeStats'
          }
        });
        
        if (result.result.success) {
          this.setData({
            realTimeStats: result.result.data
          });
        }
        
      } catch (error) {
        console.error('加载实时统计数据失败:', error);
      }
    },

    /**
     * 加载权限使用统计
     */
    async loadPermissionUsage() {
      try {
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',
          data: {
            action: 'getPermissionUsageStats'
          }
        });
        
        if (result.result.success) {
          this.setData({
            permissionUsage: result.result.data
          });
        }
        
      } catch (error) {
        console.error('加载权限使用统计失败:', error);
      }
    },

    /**
     * 加载用户活动统计
     */
    async loadUserActivity() {
      try {
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',
          data: {
            action: 'getUserActivityStats'
          }
        });
        
        if (result.result.success) {
          this.setData({
            userActivity: result.result.data
          });
        }
        
      } catch (error) {
        console.error('加载用户活动统计失败:', error);
      }
    },

    /**
     * 加载性能指标
     */
    async loadPerformanceMetrics() {
      try {
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',
          data: {
            action: 'getPerformanceMetrics'
          }
        });
        
        if (result.result.success) {
          this.setData({
            performanceMetrics: result.result.data
          });
        }
        
      } catch (error) {
        console.error('加载性能指标失败:', error);
      }
    },

    /**
     * 加载系统警告
     */
    async loadSystemAlerts() {
      try {
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',
          data: {
            action: 'getSystemAlerts'
          }
        });
        
        if (result.result.success) {
          this.setData({
            systemAlerts: result.result.data
          });
        }
        
      } catch (error) {
        console.error('加载系统警告失败:', error);
      }
    },

    /**
     * 加载图表数据
     */
    async loadChartData() {
      try {
        const result = await wx.cloud.callFunction({
          name: 'adminManagement',
          data: {
            action: 'getMonitoringChartData'
          }
        });
        
        if (result.result.success) {
          this.setData({
            chartData: result.result.data
          });
        }
        
      } catch (error) {
        console.error('加载图表数据失败:', error);
      }
    },

    /**
     * 启动自动刷新
     */
    startAutoRefresh() {
      this.clearRefreshTimer();
      
      this.data.refreshTimer = setInterval(() => {
        this.loadAllMonitoringData();
      }, this.data.refreshInterval * 1000);
    },

    /**
     * 清除刷新定时器
     */
    clearRefreshTimer() {
      if (this.data.refreshTimer) {
        clearInterval(this.data.refreshTimer);
        this.setData({
          refreshTimer: null
        });
      }
    },

    /**
     * 手动刷新数据
     */
    async refreshData() {
      this.setData({ loading: true });
      
      try {
        await this.loadAllMonitoringData();
        
        wx.showToast({
          title: '刷新成功',
          icon: 'success'
        });
        
        // 触发刷新事件
        this.triggerEvent('dataRefresh', {
          timestamp: new Date().getTime()
        });
        
      } catch (error) {
        wx.showToast({
          title: '刷新失败',
          icon: 'error'
        });
      } finally {
        this.setData({ loading: false });
      }
    },

    /**
     * 显示详情模态框
     */
    showDetail(e) {
      const { type, data } = e.currentTarget.dataset;
      
      this.setData({
        showDetailModal: true,
        detailType: type,
        currentDetail: data
      });
    },

    /**
     * 关闭详情模态框
     */
    closeDetail() {
      this.setData({
        showDetailModal: false,
        detailType: '',
        currentDetail: null
      });
    },

    /**
     * 获取系统健康状态颜色
     */
    getHealthStatusColor(status) {
      const colors = {
        'good': '#52c41a',
        'healthy': '#52c41a',
        'warning': '#faad14',
        'error': '#f5222d',
        'unhealthy': '#f5222d'
      };
      
      return colors[status] || '#8c8c8c';
    },

    /**
     * 获取系统健康状态图标
     */
    getHealthStatusIcon(status) {
      const icons = {
        'good': '✅',
        'healthy': '✅',
        'warning': '⚠️',
        'error': '❌',
        'unhealthy': '❌'
      };
      
      return icons[status] || '❓';
    },

    /**
     * 格式化数字
     */
    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
      }
      return num.toString();
    },

    /**
     * 格式化百分比
     */
    formatPercentage(value) {
      return (value * 100).toFixed(1) + '%';
    },

    /**
     * 处理警告点击
     */
    handleAlertClick(e) {
      const { alert } = e.currentTarget.dataset;
      
      // 触发警告处理事件
      this.triggerEvent('alertHandle', {
        alert: alert
      });
    },

    /**
     * 切换自动刷新
     */
    toggleAutoRefresh() {
      const autoRefresh = !this.data.autoRefresh;
      
      this.setData({
        autoRefresh
      });
      
      // 触发自动刷新状态变更事件
      this.triggerEvent('autoRefreshToggle', {
        autoRefresh
      });
    },

    /**
     * 设置刷新间隔
     */
    setRefreshInterval(e) {
      const { interval } = e.currentTarget.dataset;
      
      this.setData({
        refreshInterval: parseInt(interval)
      });
      
      // 重启自动刷新
      if (this.data.autoRefresh) {
        this.startAutoRefresh();
      }
      
      // 触发刷新间隔变更事件
      this.triggerEvent('refreshIntervalChange', {
        interval: parseInt(interval)
      });
    }
  }
});
