/* components/admin/permission-config/permission-config.wxss */
/* 权限可视化配置组件样式 */

.permission-config {
  background: #f5f7fa;
  min-height: 100vh;
}

/* ==================== 配置头部 ==================== */
.config-header {
  background: white;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.header-info {
  flex: 1;
}

.config-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.config-subtitle {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.header-actions {
  display: flex;
  align-items: center;
}

.display-mode-switch {
  display: flex;
  background: #f0f0f0;
  border-radius: 8rpx;
  overflow: hidden;
}

.mode-btn {
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  color: #666;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mode-btn.active {
  background: #667eea;
  color: white;
}

/* ==================== 搜索栏 ==================== */
.search-bar {
  position: relative;
  margin: 20rpx 30rpx;
}

.search-input {
  width: 100%;
  padding: 24rpx 60rpx 24rpx 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: white;
}

.search-icon {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #999;
}

/* ==================== 权限统计 ==================== */
.permission-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin: 20rpx 30rpx;
}

.stat-item {
  background: white;
  padding: 30rpx;
  border-radius: 12rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.stat-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* ==================== 矩阵视图 ==================== */
.matrix-view {
  margin: 20rpx 30rpx;
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.matrix-header {
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.matrix-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.matrix-subtitle {
  font-size: 24rpx;
  color: #666;
}

.matrix-container {
  max-height: 600rpx;
  overflow: auto;
}

.matrix-table {
  display: table;
  width: 100%;
  min-width: 1200rpx;
}

.matrix-row {
  display: table-row;
}

.matrix-row.header-row {
  background: #f8f9fa;
  font-weight: bold;
}

.matrix-cell {
  display: table-cell;
  padding: 16rpx;
  border-bottom: 1rpx solid #e9ecef;
  border-right: 1rpx solid #e9ecef;
  vertical-align: middle;
  text-align: center;
}

.matrix-cell.role-cell {
  min-width: 200rpx;
  text-align: left;
  background: #f8f9fa;
  position: sticky;
  left: 0;
  z-index: 10;
}

.matrix-cell.permission-cell {
  min-width: 120rpx;
}

.role-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.permission-name {
  font-size: 22rpx;
  color: #666;
  writing-mode: vertical-lr;
  text-orientation: mixed;
}

.permission-checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 auto;
}

.permission-checkbox:not(.checked) {
  background: #f0f0f0;
  color: #999;
}

.permission-checkbox.checked {
  background: #667eea;
  color: white;
}

/* ==================== 树形视图 ==================== */
.tree-view {
  margin: 20rpx 30rpx;
}

.permission-group {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.group-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #f8f9fa;
  border-bottom: 2rpx solid #e9ecef;
}

.group-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.group-info {
  flex: 1;
}

.group-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.group-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.group-count {
  font-size: 24rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}

.group-permissions {
  padding: 20rpx;
}

.permission-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.permission-item:hover {
  background: #e9ecef;
}

.permission-item .permission-checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.permission-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.permission-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.permission-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.permission-category {
  font-size: 22rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  align-self: flex-start;
}

.permission-detail-btn {
  font-size: 24rpx;
  color: #667eea;
  padding: 8rpx 16rpx;
  border: 1rpx solid #667eea;
  border-radius: 6rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.permission-detail-btn:hover {
  background: #667eea;
  color: white;
}

/* ==================== 列表视图 ==================== */
.list-view {
  margin: 20rpx 30rpx;
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.list-header {
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  background: #f8f9fa;
}

.list-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.list-subtitle {
  font-size: 24rpx;
  color: #666;
}

.permission-list {
  max-height: 800rpx;
  overflow-y: auto;
}

.list-permission-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.list-permission-item:hover {
  background: #f8f9fa;
}

.list-permission-checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  margin-top: 8rpx;
  flex-shrink: 0;
}

.list-permission-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.list-permission-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.list-permission-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.list-permission-category {
  font-size: 22rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}

.list-permission-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.list-permission-key {
  font-size: 22rpx;
  color: #999;
  font-family: monospace;
}

/* ==================== 操作按钮 ==================== */
.config-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  border-top: 2rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  max-width: 600rpx;
  margin: 0 auto;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: #667eea;
  color: white;
}

.action-btn.primary:hover {
  background: #5a6fd8;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

.action-btn.secondary:hover {
  background: #e9ecef;
}

/* ==================== 权限详情弹窗 ==================== */
.permission-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: white;
  border-radius: 16rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  position: relative;
  z-index: 1001;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-item {
  margin-bottom: 24rpx;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.related-roles {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 8rpx;
}

.role-tag {
  font-size: 22rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .permission-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .config-header {
    flex-direction: column;
    gap: 20rpx;
    align-items: stretch;
  }
  
  .display-mode-switch {
    justify-content: center;
  }
  
  .matrix-container {
    max-height: 400rpx;
  }
  
  .permission-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }
  
  .permission-item .permission-checkbox {
    margin-right: 0;
  }
}
