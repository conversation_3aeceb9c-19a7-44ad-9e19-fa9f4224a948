<!--components/admin/permission-config/permission-config.wxml-->
<!-- 权限可视化配置组件 - 基于57个权限常量的可视化权限配置界面 -->

<view class="permission-config">
  <!-- 配置头部 -->
  <view class="config-header">
    <view class="header-info">
      <text class="config-title">权限配置管理</text>
      <text class="config-subtitle" wx:if="{{currentRole}}">
        当前配置角色：{{roleList.find(r => r.key === currentRole).name}}
      </text>
    </view>
    
    <view class="header-actions">
      <!-- 显示模式切换 -->
      <view class="display-mode-switch">
        <view 
          class="mode-btn {{displayMode === 'matrix' ? 'active' : ''}}"
          bindtap="switchDisplayMode"
          data-mode="matrix"
        >
          矩阵视图
        </view>
        <view 
          class="mode-btn {{displayMode === 'tree' ? 'active' : ''}}"
          bindtap="switchDisplayMode"
          data-mode="tree"
        >
          树形视图
        </view>
        <view 
          class="mode-btn {{displayMode === 'list' ? 'active' : ''}}"
          bindtap="switchDisplayMode"
          data-mode="list"
        >
          列表视图
        </view>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-bar">
    <input 
      class="search-input"
      placeholder="搜索权限..."
      value="{{searchKeyword}}"
      bindinput="onSearchInput"
    />
    <view class="search-icon">🔍</view>
  </view>

  <!-- 权限统计 -->
  <view class="permission-stats">
    <view class="stat-item">
      <view class="stat-value">{{permissionGroups.platform.permissions.length}}</view>
      <view class="stat-label">平台级权限</view>
    </view>
    <view class="stat-item">
      <view class="stat-value">{{permissionGroups.tenant.permissions.length}}</view>
      <view class="stat-label">租户级权限</view>
    </view>
    <view class="stat-item">
      <view class="stat-value">{{permissionGroups.common.permissions.length}}</view>
      <view class="stat-label">通用权限</view>
    </view>
    <view class="stat-item">
      <view class="stat-value">{{selectedPermissions.length}}</view>
      <view class="stat-label">已选权限</view>
    </view>
  </view>

  <!-- 矩阵视图 -->
  <view class="matrix-view" wx:if="{{displayMode === 'matrix'}}">
    <view class="matrix-header">
      <view class="matrix-title">权限矩阵</view>
      <view class="matrix-subtitle">横轴：权限，纵轴：角色</view>
    </view>
    
    <scroll-view class="matrix-container" scroll-x scroll-y>
      <view class="matrix-table">
        <!-- 表头 -->
        <view class="matrix-row header-row">
          <view class="matrix-cell role-cell">角色/权限</view>
          <view 
            class="matrix-cell permission-cell"
            wx:for="{{Object.values(PERMISSIONS)}}"
            wx:key="*this"
          >
            <text class="permission-name">{{getPermissionName(item)}}</text>
          </view>
        </view>
        
        <!-- 数据行 -->
        <view 
          class="matrix-row"
          wx:for="{{permissionMatrix}}"
          wx:key="role"
        >
          <view class="matrix-cell role-cell">
            <text class="role-name">{{item.roleName}}</text>
          </view>
          <view 
            class="matrix-cell permission-cell"
            wx:for="{{Object.values(PERMISSIONS)}}"
            wx:for-item="permission"
            wx:key="*this"
          >
            <view 
              class="permission-checkbox {{item.permissions[permission] ? 'checked' : ''}}"
              bindtap="togglePermission"
              data-permission="{{permission}}"
            >
              {{item.permissions[permission] ? '✓' : '○'}}
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 树形视图 -->
  <view class="tree-view" wx:if="{{displayMode === 'tree'}}">
    <!-- 平台级权限组 -->
    <view class="permission-group">
      <view class="group-header">
        <view class="group-icon">🏢</view>
        <view class="group-info">
          <text class="group-name">{{permissionGroups.platform.name}}</text>
          <text class="group-desc">{{permissionGroups.platform.description}}</text>
        </view>
        <view class="group-count">{{permissionGroups.platform.permissions.length}}个权限</view>
      </view>
      
      <view class="group-permissions">
        <view 
          class="permission-item"
          wx:for="{{permissionGroups.platform.permissions}}"
          wx:key="key"
        >
          <view 
            class="permission-checkbox {{selectedPermissions.includes(item.value) ? 'checked' : ''}}"
            bindtap="togglePermission"
            data-permission="{{item.value}}"
          >
            {{selectedPermissions.includes(item.value) ? '✓' : '○'}}
          </view>
          <view class="permission-content">
            <text class="permission-name">{{item.name}}</text>
            <text class="permission-desc">{{item.description}}</text>
            <view class="permission-category">{{item.category}}</view>
          </view>
          <view 
            class="permission-detail-btn"
            bindtap="viewPermissionDetail"
            data-permission="{{item.key}}"
          >
            详情
          </view>
        </view>
      </view>
    </view>

    <!-- 租户级权限组 -->
    <view class="permission-group">
      <view class="group-header">
        <view class="group-icon">🏠</view>
        <view class="group-info">
          <text class="group-name">{{permissionGroups.tenant.name}}</text>
          <text class="group-desc">{{permissionGroups.tenant.description}}</text>
        </view>
        <view class="group-count">{{permissionGroups.tenant.permissions.length}}个权限</view>
      </view>
      
      <view class="group-permissions">
        <view 
          class="permission-item"
          wx:for="{{permissionGroups.tenant.permissions}}"
          wx:key="key"
        >
          <view 
            class="permission-checkbox {{selectedPermissions.includes(item.value) ? 'checked' : ''}}"
            bindtap="togglePermission"
            data-permission="{{item.value}}"
          >
            {{selectedPermissions.includes(item.value) ? '✓' : '○'}}
          </view>
          <view class="permission-content">
            <text class="permission-name">{{item.name}}</text>
            <text class="permission-desc">{{item.description}}</text>
            <view class="permission-category">{{item.category}}</view>
          </view>
          <view 
            class="permission-detail-btn"
            bindtap="viewPermissionDetail"
            data-permission="{{item.key}}"
          >
            详情
          </view>
        </view>
      </view>
    </view>

    <!-- 通用权限组 -->
    <view class="permission-group">
      <view class="group-header">
        <view class="group-icon">⚙️</view>
        <view class="group-info">
          <text class="group-name">{{permissionGroups.common.name}}</text>
          <text class="group-desc">{{permissionGroups.common.description}}</text>
        </view>
        <view class="group-count">{{permissionGroups.common.permissions.length}}个权限</view>
      </view>
      
      <view class="group-permissions">
        <view 
          class="permission-item"
          wx:for="{{permissionGroups.common.permissions}}"
          wx:key="key"
        >
          <view 
            class="permission-checkbox {{selectedPermissions.includes(item.value) ? 'checked' : ''}}"
            bindtap="togglePermission"
            data-permission="{{item.value}}"
          >
            {{selectedPermissions.includes(item.value) ? '✓' : '○'}}
          </view>
          <view class="permission-content">
            <text class="permission-name">{{item.name}}</text>
            <text class="permission-desc">{{item.description}}</text>
            <view class="permission-category">{{item.category}}</view>
          </view>
          <view 
            class="permission-detail-btn"
            bindtap="viewPermissionDetail"
            data-permission="{{item.key}}"
          >
            详情
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 列表视图 -->
  <view class="list-view" wx:if="{{displayMode === 'list'}}">
    <view class="list-header">
      <text class="list-title">权限列表</text>
      <text class="list-subtitle">共57个权限</text>
    </view>
    
    <view class="permission-list">
      <view 
        class="list-permission-item"
        wx:for="{{Object.entries(permissionGroups).map(([key, group]) => group.permissions).flat()}}"
        wx:key="key"
      >
        <view 
          class="list-permission-checkbox {{selectedPermissions.includes(item.value) ? 'checked' : ''}}"
          bindtap="togglePermission"
          data-permission="{{item.value}}"
        >
          {{selectedPermissions.includes(item.value) ? '✓' : '○'}}
        </view>
        <view class="list-permission-content">
          <view class="list-permission-header">
            <text class="list-permission-name">{{item.name}}</text>
            <view class="list-permission-category">{{item.category}}</view>
          </view>
          <text class="list-permission-desc">{{item.description}}</text>
          <text class="list-permission-key">{{item.key}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="config-actions" wx:if="{{!readonly}}">
    <view class="action-buttons">
      <button class="action-btn secondary" bindtap="resetPermissionConfig">
        重置
      </button>
      <button class="action-btn primary" bindtap="savePermissionConfig">
        保存配置
      </button>
    </view>
  </view>

  <!-- 权限详情弹窗 -->
  <view class="permission-detail-modal" wx:if="{{showPermissionDetail}}">
    <view class="modal-overlay" bindtap="closePermissionDetail"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">权限详情</text>
        <view class="modal-close" bindtap="closePermissionDetail">×</view>
      </view>
      
      <view class="modal-body" wx:if="{{currentPermissionDetail}}">
        <view class="detail-item">
          <text class="detail-label">权限名称：</text>
          <text class="detail-value">{{currentPermissionDetail.name}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">权限键名：</text>
          <text class="detail-value">{{currentPermissionDetail.key}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">权限分类：</text>
          <text class="detail-value">{{currentPermissionDetail.category}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">权限描述：</text>
          <text class="detail-value">{{currentPermissionDetail.description}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">相关角色：</text>
          <view class="related-roles">
            <view 
              class="role-tag"
              wx:for="{{currentPermissionDetail.relatedRoles}}"
              wx:key="role"
            >
              {{item.name}}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
