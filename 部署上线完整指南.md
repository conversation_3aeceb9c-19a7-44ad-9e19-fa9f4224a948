# 智慧养鹅SAAS平台部署上线完整指南

> 基于Context7最佳实践的微信小程序云开发平台完整部署方案
> 
> **生成时间**: 2025年8月30日  
> **适用版本**: 智慧养鹅SAAS v1.0  
> **技术架构**: 微信小程序 + 云开发 + 多租户架构

## 📋 目录

- [第一部分: 系统架构概述](#第一部分-系统架构概述)
- [第二部分: 环境准备与配置](#第二部分-环境准备与配置)
- [第三部分: 云开发环境部署](#第三部分-云开发环境部署)
- [第四部分: 数据库初始化](#第四部分-数据库初始化)
- [第五部分: 云函数部署](#第五部分-云函数部署)
- [第六部分: 小程序端配置](#第六部分-小程序端配置)
- [第七部分: 生产环境上线](#第七部分-生产环境上线)
- [第八部分: 监控与运维](#第八部分-监控与运维)
- [第九部分: 安全与合规](#第九部分-安全与合规)
- [第十部分: 故障排查手册](#第十部分-故障排查手册)

---

## 第一部分: 系统架构概述

### 1.1 整体架构设计

```mermaid
graph TB
    User[用户] --> MP[微信小程序端]
    MP --> Cloud[微信云开发]
    Cloud --> DB[(云数据库)]
    Cloud --> Storage[云存储]
    Cloud --> CF[云函数]
    
    subgraph "多租户架构"
        CF --> Auth[身份认证]
        CF --> Tenant[租户管理]
        CF --> Admin[管理后台]
        CF --> Business[业务逻辑]
    end
    
    subgraph "数据隔离"
        DB --> TenantA[租户A数据]
        DB --> TenantB[租户B数据]
        DB --> Platform[平台数据]
    end
```

### 1.2 核心技术栈

- **前端**: 微信小程序原生开发
- **后端**: 微信云开发 + Node.js
- **数据库**: 云数据库 (NoSQL + 关系型支持)
- **存储**: 云存储 (图片、文档、导出文件)
- **认证**: 微信授权 + JWT令牌系统
- **架构**: 多租户SAAS架构，数据完全隔离

### 1.3 目录结构解析

```
智慧养鹅云开发/
├── app.js                    # 小程序入口文件
├── app.json                  # 小程序配置文件
├── project.config.json       # 项目配置文件
├── pages/                    # 页面文件
│   ├── home/                 # 首页模块
│   ├── production/           # 生产管理模块
│   ├── shop/                 # 商城模块
│   ├── profile/              # 个人中心
│   └── workspace/            # OA办公模块
├── components/               # 公共组件
│   ├── common/               # 通用组件
│   ├── chart/                # 图表组件
│   └── form-builder/         # 表单构建器
├── utils/                    # 工具函数
│   ├── api.js                # API接口管理
│   ├── tenant-config.js      # 租户配置
│   ├── environment-config.js # 环境配置
│   └── role-permission.js    # 权限管理
├── cloudfunctions/           # 云函数目录
│   ├── auth/                 # 认证相关云函数
│   ├── business/             # 业务逻辑云函数
│   └── admin/                # 管理后台云函数
├── constants/                # 常量配置
│   ├── admin-api-mapping.constants.js  # 管理API映射
│   └── api.constants.js      # API常量配置
└── assets/                   # 静态资源
    └── icons/                # 图标资源
```

### 1.4 数据流转架构

```
用户操作 → 小程序页面 → API层(utils/api.js) → 云函数适配器 → 对应云函数 → 数据库操作(secureQuery) → 响应返回
```

---

## 第二部分: 环境准备与配置

### 2.1 开发工具准备

#### 必需工具清单

1. **微信开发者工具** (最新稳定版)
   - 下载地址: https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html
   - 最低版本要求: 1.06.2202040

2. **Node.js环境** (LTS版本)
   - 推荐版本: Node.js 16.x 或 18.x
   - npm版本: 8.x+

3. **微信开发者账号**
   - 个人或企业开发者账号
   - 已完成小程序创建和AppID获取

#### 环境变量配置

创建环境配置文件：

```bash
# .env.development (开发环境)
NODE_ENV=development
CLOUD_ENV=your-dev-env-id
API_BASE_URL=http://localhost:3001
DEBUG=true
ENABLE_MOCK=true

# .env.production (生产环境)
NODE_ENV=production
CLOUD_ENV=your-prod-env-id
API_BASE_URL=https://api.zhihuiyange.com
DEBUG=false
ENABLE_MOCK=false

# .env.testing (测试环境)
NODE_ENV=testing
CLOUD_ENV=your-test-env-id
API_BASE_URL=https://test-api.zhihuiyange.com
DEBUG=true
ENABLE_MOCK=false
```

### 2.2 微信云开发环境准备

#### 2.2.1 创建云开发环境

1. 登录微信公众平台 (https://mp.weixin.qq.com)
2. 进入小程序管理后台
3. 左侧菜单选择"云开发" → "环境"
4. 点击"新建环境"

**环境配置建议:**

```yaml
开发环境:
  环境名称: dev-zhihuiyange
  环境ID: zhihuiyange-dev-xxx
  数据库: 1GB (免费额度)
  存储: 2GB (免费额度)
  云函数: 10万次/月 (免费额度)

测试环境:
  环境名称: test-zhihuiyange
  环境ID: zhihuiyange-test-xxx
  数据库: 3GB
  存储: 5GB
  云函数: 50万次/月

生产环境:
  环境名称: prod-zhihuiyange
  环境ID: zhihuiyange-prod-xxx
  数据库: 10GB+
  存储: 20GB+
  云函数: 200万次/月+
```

#### 2.2.2 云开发资源配额规划

| 资源类型 | 开发环境 | 测试环境 | 生产环境 |
|---------|----------|----------|----------|
| 数据库容量 | 1GB | 3GB | 10GB+ |
| 存储容量 | 2GB | 5GB | 20GB+ |
| 云函数调用 | 10万次/月 | 50万次/月 | 200万次/月+ |
| CDN流量 | 1GB/月 | 5GB/月 | 50GB/月+ |
| 数据库读写 | 5万次/日 | 50万次/日 | 500万次/日+ |

### 2.3 项目配置文件详解

#### 2.3.1 project.config.json 配置

```json
{
  "description": "智慧养鹅SAAS平台云开发配置",
  "miniprogramRoot": "./",
  "cloudfunctionRoot": "cloudfunctions/",
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "minified": true,
    "newFeature": true,
    "coverView": true,
    "nodeModules": true,
    "autoAudits": false,
    "showShadowRootInWxmlPanel": true,
    "useCompilerPlugins": false,
    "useMultiFrameRuntime": true,
    "useApiHook": true,
    "useApiHostProcess": true
  },
  "appid": "wx68a4d75dd80665ec",
  "projectname": "智慧养鹅SAAS平台",
  "libVersion": "2.32.3",
  "cloudfunctionTemplateRoot": "cloudfunctionTemplate/",
  "compileType": "miniprogram"
}
```

#### 2.3.2 app.json 小程序配置

关键配置说明：

```json
{
  "networkTimeout": {
    "request": 15000,
    "downloadFile": 15000,
    "uploadFile": 15000,
    "connectSocket": 15000
  },
  "permission": {
    "scope.userLocation": {
      "desc": "用于获取位置信息，提供本地化服务"
    }
  },
  "requiredBackgroundModes": ["audio"],
  "lazyCodeLoading": "requiredComponents"
}
```

---

## 第三部分: 云开发环境部署

### 3.1 云开发初始化

#### 3.1.1 初始化云开发SDK

在 `app.js` 中配置云开发初始化：

```javascript
// app.js
App({
  onLaunch: function () {
    // 初始化云开发
    this.initCloudEnvironment();
  },

  // 初始化云开发环境
  initCloudEnvironment: function() {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
      return;
    }

    wx.cloud.init({
      env: 'your-env-id', // 替换为实际的云环境ID
      traceUser: true
    });

    // 设置全局云开发实例
    this.globalData.cloud = wx.cloud;
    this.globalData.db = wx.cloud.database();
    this.globalData.isCloudEnabled = true;

    console.log('云开发环境初始化成功');
  }
});
```

### 3.2 云函数结构规划

#### 3.2.1 云函数分类架构

```
cloudfunctions/
├── auth/                     # 认证相关 (用户登录、授权)
│   ├── login/
│   └── getUserInfo/
├── business/                 # 业务逻辑 (核心业务功能)
│   ├── flockManagement/      # 鹅群管理
│   ├── healthManagement/     # 健康管理
│   ├── materialManagement/   # 物料管理
│   └── shopManagement/       # 商城管理
└── admin/                    # 管理后台 (平台管理功能)
    ├── adminManagement/      # 基础管理
    ├── systemManagement/     # 系统管理
    ├── tenantManagement/     # 租户管理
    ├── analytics/            # 数据分析
    ├── healthMonitoring/     # 系统监控
    └── auditLog/             # 审计日志
```

#### 3.2.2 云函数标准结构

每个云函数标准目录结构：

```
cloudfunction-name/
├── index.js          # 云函数入口文件
├── package.json      # 依赖配置
└── config.json       # 云函数配置 (可选)
```

标准云函数入口模板：

```javascript
// index.js 标准模板
const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { action, data = {} } = event;
  const { openid, appid } = cloud.getWXContext();

  try {
    // 用户身份验证
    const userInfo = await getUserInfo(openid);
    
    // 租户权限验证
    await validateTenantAccess(userInfo, data);

    // 路由到具体业务逻辑
    switch (action) {
      case 'actionName':
        return await handleAction(data, userInfo);
      default:
        throw new Error('不支持的操作');
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      success: false,
      error: error.message
    };
  }
};
```

### 3.3 云函数部署步骤

#### 3.3.1 批量部署脚本

创建部署脚本 `scripts/deploy-cloudfunctions.js`:

```javascript
/**
 * 云函数批量部署脚本
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const CLOUD_FUNCTIONS = [
  'auth/login',
  'auth/getUserInfo',
  'business/flockManagement',
  'business/healthManagement',
  'business/materialManagement',
  'business/shopManagement',
  'admin/adminManagement',
  'admin/systemManagement',
  'admin/tenantManagement',
  'admin/analytics',
  'admin/healthMonitoring',
  'admin/auditLog'
];

async function deployCloudFunctions() {
  console.log('开始部署云函数...');
  
  for (const funcPath of CLOUD_FUNCTIONS) {
    try {
      console.log(`部署云函数: ${funcPath}`);
      
      // 进入云函数目录
      const fullPath = path.join(__dirname, '../cloudfunctions', funcPath);
      process.chdir(fullPath);
      
      // 安装依赖
      execSync('npm install', { stdio: 'inherit' });
      
      // 部署云函数
      execSync('tcb fn deploy', { stdio: 'inherit' });
      
      console.log(`✅ ${funcPath} 部署成功`);
    } catch (error) {
      console.error(`❌ ${funcPath} 部署失败:`, error.message);
    }
  }
  
  console.log('云函数部署完成!');
}

deployCloudFunctions();
```

#### 3.3.2 单个云函数部署流程

1. **安装云开发命令行工具**
   ```bash
   npm install -g @cloudbase/cli
   ```

2. **登录云开发**
   ```bash
   tcb login
   ```

3. **部署云函数**
   ```bash
   # 进入云函数目录
   cd cloudfunctions/auth/login
   
   # 安装依赖
   npm install
   
   # 部署到云端
   tcb fn deploy --name login --env your-env-id
   ```

4. **验证部署结果**
   ```bash
   # 查看云函数列表
   tcb fn list --env your-env-id
   
   # 查看云函数详情
   tcb fn detail --name login --env your-env-id
   ```

---

## 第四部分: 数据库初始化

### 4.1 数据库设计架构

#### 4.1.1 多租户数据隔离模型

```javascript
// 数据库集合设计
const DATABASE_COLLECTIONS = {
  // 平台级数据 (无租户隔离)
  platform_config: '平台配置',
  goose_price: '今日鹅价',
  platform_announcements: '平台公告',
  knowledge_base: '知识库',
  
  // 租户级数据 (需要tenant_id隔离)
  tenants: '租户信息',
  users: '用户数据',
  flocks: '鹅群数据',
  health_records: '健康记录',
  materials: '物料数据',
  shop_products: '商城商品',
  orders: '订单数据',
  financial_records: '财务记录',
  
  // 系统级数据 (管理后台使用)
  operation_logs: '操作日志',
  security_audit_logs: '安全审计日志',
  system_health_history: '系统健康历史',
  system_metrics: '系统监控指标'
};
```

#### 4.1.2 核心集合字段设计

```javascript
// users 集合字段设计
const USER_SCHEMA = {
  _id: 'String',              // 用户ID
  openid: 'String',           // 微信openid
  tenant_id: 'String',        // 租户ID (关键字段)
  nickname: 'String',         // 用户昵称
  avatar: 'String',           // 头像URL
  role: 'String',             // 用户角色
  status: 'String',           // 用户状态 (active/inactive/suspended)
  permissions: 'Array',       // 用户权限列表
  created_time: 'Date',       // 创建时间
  last_login_time: 'Date',    // 最后登录时间
  profile: 'Object',          // 用户详细信息
  tenant_info: 'Object'       // 租户相关信息
};

// tenants 集合字段设计
const TENANT_SCHEMA = {
  _id: 'String',              // 租户ID
  tenant_code: 'String',      // 租户代码 (唯一标识)
  tenant_name: 'String',      // 租户名称
  owner_id: 'String',         // 所有者用户ID
  subscription_plan: 'String', // 订阅计划
  status: 'String',           // 租户状态
  config: 'Object',           // 租户配置信息
  created_time: 'Date',       // 创建时间
  expire_time: 'Date'         // 到期时间
};
```

### 4.2 数据库初始化脚本

#### 4.2.1 创建初始化云函数

创建 `cloudfunctions/admin/dbInit/index.js`:

```javascript
/**
 * 数据库初始化云函数
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 集合索引配置
const COLLECTION_INDEXES = {
  users: [
    { keys: { tenant_id: 1 }, name: 'tenant_id_index' },
    { keys: { openid: 1 }, name: 'openid_index', unique: true },
    { keys: { tenant_id: 1, role: 1 }, name: 'tenant_role_index' }
  ],
  tenants: [
    { keys: { tenant_code: 1 }, name: 'tenant_code_index', unique: true },
    { keys: { status: 1 }, name: 'status_index' }
  ],
  flocks: [
    { keys: { tenant_id: 1 }, name: 'tenant_id_index' },
    { keys: { tenant_id: 1, user_id: 1 }, name: 'tenant_user_index' },
    { keys: { created_time: -1 }, name: 'created_time_index' }
  ],
  operation_logs: [
    { keys: { tenant_id: 1 }, name: 'tenant_id_index' },
    { keys: { user_id: 1 }, name: 'user_id_index' },
    { keys: { timestamp: -1 }, name: 'timestamp_index' }
  ]
};

// 默认数据初始化
const DEFAULT_DATA = {
  platform_config: [
    {
      _id: 'system_config',
      app_name: '智慧养鹅SAAS平台',
      version: '1.0.0',
      maintenance_mode: false,
      registration_enabled: true,
      default_subscription_plan: 'basic',
      created_time: new Date()
    }
  ],
  tenants: [
    {
      _id: 'demo_tenant',
      tenant_code: 'DEMO001',
      tenant_name: '演示租户',
      owner_id: 'demo_user',
      subscription_plan: 'basic',
      status: 'active',
      config: {
        max_users: 10,
        max_flocks: 100,
        features: ['flock_management', 'health_monitoring', 'basic_reports']
      },
      created_time: new Date(),
      expire_time: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1年后到期
    }
  ]
};

exports.main = async (event, context) => {
  const { action } = event;
  
  try {
    switch (action) {
      case 'createIndexes':
        return await createDatabaseIndexes();
      case 'initDefaultData':
        return await initializeDefaultData();
      case 'validateSchema':
        return await validateDatabaseSchema();
      default:
        throw new Error('不支持的初始化操作');
    }
  } catch (error) {
    console.error('数据库初始化失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

async function createDatabaseIndexes() {
  const results = [];
  
  for (const [collection, indexes] of Object.entries(COLLECTION_INDEXES)) {
    for (const index of indexes) {
      try {
        await db.collection(collection).createIndex(index);
        results.push(`✅ ${collection}.${index.name} 索引创建成功`);
      } catch (error) {
        results.push(`❌ ${collection}.${index.name} 索引创建失败: ${error.message}`);
      }
    }
  }
  
  return { success: true, results };
}

async function initializeDefaultData() {
  const results = [];
  
  for (const [collection, data] of Object.entries(DEFAULT_DATA)) {
    for (const record of data) {
      try {
        await db.collection(collection).add({
          data: record
        });
        results.push(`✅ ${collection} 默认数据初始化成功`);
      } catch (error) {
        results.push(`❌ ${collection} 默认数据初始化失败: ${error.message}`);
      }
    }
  }
  
  return { success: true, results };
}
```

#### 4.2.2 数据库初始化执行步骤

1. **部署初始化云函数**
   ```bash
   cd cloudfunctions/admin/dbInit
   npm install
   tcb fn deploy --name dbInit --env your-env-id
   ```

2. **执行索引创建**
   ```javascript
   // 在微信开发者工具控制台执行
   wx.cloud.callFunction({
     name: 'dbInit',
     data: {
       action: 'createIndexes'
     }
   }).then(res => {
     console.log('索引创建结果:', res.result);
   });
   ```

3. **初始化默认数据**
   ```javascript
   wx.cloud.callFunction({
     name: 'dbInit',
     data: {
       action: 'initDefaultData'
     }
   }).then(res => {
     console.log('默认数据初始化结果:', res.result);
   });
   ```

### 4.3 数据迁移与备份策略

#### 4.3.1 数据备份云函数

```javascript
/**
 * 数据备份云函数
 * cloudfunctions/admin/dataBackup/index.js
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { collections, backup_name } = event;
  
  try {
    const backupData = {};
    
    for (const collection of collections) {
      const { data } = await db.collection(collection).get();
      backupData[collection] = data;
    }
    
    // 将备份数据上传到云存储
    const backupFileName = `backup_${backup_name}_${Date.now()}.json`;
    const result = await cloud.uploadFile({
      cloudPath: `backups/${backupFileName}`,
      fileContent: Buffer.from(JSON.stringify(backupData, null, 2))
    });
    
    return {
      success: true,
      backup_file: result.fileID,
      backup_size: Object.keys(backupData).length,
      created_time: new Date().toISOString()
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
};
```

---

## 第五部分: 云函数部署

### 5.1 云函数依赖管理

#### 5.1.1 统一依赖配置

在每个云函数的 `package.json` 中配置标准依赖：

```json
{
  "name": "cloudfunction-name",
  "version": "1.0.0",
  "description": "云函数描述",
  "main": "index.js",
  "dependencies": {
    "wx-server-sdk": "~2.6.3"
  },
  "devDependencies": {},
  "scripts": {
    "test": "echo \"Error: no test specified\" && exit 1"
  }
}
```

#### 5.1.2 公共模块管理

创建公共模块目录 `cloudfunctions/common/`:

```
cloudfunctions/common/
├── utils/
│   ├── api-handler.js        # API统一处理器
│   ├── api-errors.js         # 错误定义
│   ├── role-permission.js    # 权限管理
│   └── data-validation.js    # 数据验证
├── config/
│   ├── database.js           # 数据库配置
│   └── constants.js          # 常量配置
└── middleware/
    ├── auth.js               # 认证中间件
    └── tenant-isolation.js   # 租户隔离中间件
```

### 5.2 核心云函数部署

#### 5.2.1 认证云函数部署

```bash
# 部署登录云函数
cd cloudfunctions/auth/login
npm install
tcb fn deploy --name login --env your-env-id

# 部署用户信息云函数
cd ../getUserInfo
npm install
tcb fn deploy --name getUserInfo --env your-env-id
```

#### 5.2.2 业务云函数部署

```bash
# 批量部署业务云函数
for func in flockManagement healthManagement materialManagement shopManagement; do
  cd cloudfunctions/business/$func
  npm install
  tcb fn deploy --name $func --env your-env-id
  cd ../../..
done
```

#### 5.2.3 管理后台云函数部署

```bash
# 批量部署管理云函数
for func in adminManagement systemManagement tenantManagement analytics healthMonitoring auditLog; do
  cd cloudfunctions/admin/$func
  npm install
  tcb fn deploy --name $func --env your-env-id
  cd ../../..
done
```

### 5.3 云函数配置优化

#### 5.3.1 性能配置

为高频调用的云函数设置性能参数：

```json
// cloudfunctions/business/flockManagement/config.json
{
  "permissions": {
    "openapi": [
      "wxacode.get",
      "wxacode.getUnlimited"
    ]
  },
  "environment": {
    "NODE_ENV": "production"
  },
  "timeout": 30,
  "memorySize": 256
}
```

#### 5.3.2 触发器配置

为定时任务配置定时触发器：

```json
// cloudfunctions/admin/systemMonitoring/config.json
{
  "triggers": [
    {
      "name": "healthCheck",
      "type": "timer",
      "config": "0 */5 * * * * *"
    }
  ]
}
```

---

## 第六部分: 小程序端配置

### 6.1 环境适配配置

#### 6.1.1 环境检测与切换

修改 `utils/environment-config.js`:

```javascript
const ENVIRONMENT_CONFIG = {
  development: {
    cloudEnv: 'zhihuiyange-dev-xxx',
    apiBaseUrl: 'http://localhost:3001',
    debug: true
  },
  testing: {
    cloudEnv: 'zhihuiyange-test-xxx',
    apiBaseUrl: 'https://test-api.zhihuiyange.com',
    debug: true
  },
  production: {
    cloudEnv: 'zhihuiyange-prod-xxx',
    apiBaseUrl: 'https://api.zhihuiyange.com',
    debug: false
  }
};

class EnvironmentConfigManager {
  constructor() {
    this.currentEnvironment = this.detectEnvironment();
  }

  detectEnvironment() {
    // 生产环境检测逻辑
    if (typeof __wxConfig !== 'undefined' && 
        __wxConfig.envVersion === 'release') {
      return 'production';
    }
    
    // 测试环境检测逻辑
    if (typeof __wxConfig !== 'undefined' && 
        __wxConfig.envVersion === 'trial') {
      return 'testing';
    }
    
    // 默认开发环境
    return 'development';
  }

  getCloudEnv() {
    return ENVIRONMENT_CONFIG[this.currentEnvironment].cloudEnv;
  }
}
```

#### 6.1.2 云开发初始化配置

修改 `app.js` 中的云开发初始化：

```javascript
const { environmentConfig } = require('./utils/environment-config');

App({
  onLaunch: function () {
    this.initCloudEnvironment();
  },

  initCloudEnvironment: function() {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
      return;
    }

    const cloudEnv = environmentConfig.getCloudEnv();
    
    wx.cloud.init({
      env: cloudEnv,
      traceUser: true
    });

    this.globalData.cloud = wx.cloud;
    this.globalData.db = wx.cloud.database();
    this.globalData.cloudEnv = cloudEnv;
    this.globalData.isCloudEnabled = true;

    console.log(`云开发环境初始化成功: ${cloudEnv}`);
  }
});
```

### 6.2 API接口配置

#### 6.2.1 云函数调用适配器

创建 `utils/cloud-api-adapter.js`:

```javascript
/**
 * 云函数API调用适配器
 */
class CloudAPIAdapter {
  constructor() {
    this.retryCount = 3;
    this.timeout = 15000;
  }

  async request(method, url, data = {}, options = {}) {
    const { fallbackToHttp = false } = options;
    
    try {
      // 解析URL获取云函数名和action
      const { functionName, action } = this.parseUrl(url);
      
      // 调用云函数
      const result = await wx.cloud.callFunction({
        name: functionName,
        data: {
          action: action,
          method: method,
          data: data
        }
      });

      if (result.result.success !== false) {
        return result.result;
      } else {
        throw new Error(result.result.error || '云函数调用失败');
      }
      
    } catch (error) {
      console.error('云函数调用失败:', error);
      
      if (fallbackToHttp) {
        // 回退到HTTP请求
        return this.httpFallback(method, url, data);
      } else {
        throw error;
      }
    }
  }

  parseUrl(url) {
    // URL路径解析逻辑
    // 例: /api/v1/flocks -> functionName: flockManagement, action: getFlocks
    const pathMapping = {
      '/api/v1/auth': 'login',
      '/api/v1/flocks': 'flockManagement',
      '/api/v1/health': 'healthManagement',
      '/api/v1/materials': 'materialManagement',
      '/api/v1/shop': 'shopManagement',
      '/admin': 'adminManagement'
    };

    // 实现URL到云函数名的映射逻辑
    for (const [path, functionName] of Object.entries(pathMapping)) {
      if (url.startsWith(path)) {
        return {
          functionName,
          action: this.extractAction(url, path)
        };
      }
    }

    throw new Error(`无法解析云函数名: ${url}`);
  }

  extractAction(url, basePath) {
    const remaining = url.replace(basePath, '');
    // 根据剩余路径确定action
    return 'defaultAction';
  }

  async httpFallback(method, url, data) {
    // HTTP请求回退逻辑
    const request = require('./request.js');
    
    switch (method.toUpperCase()) {
      case 'GET':
        return request.get(url, data);
      case 'POST':
        return request.post(url, data);
      case 'PUT':
        return request.put(url, data);
      case 'DELETE':
        return request.del(url);
      default:
        throw new Error(`不支持的HTTP方法: ${method}`);
    }
  }
}

const cloudAPIAdapter = new CloudAPIAdapter();

module.exports = {
  cloudAPIAdapter
};
```

### 6.3 权限控制配置

#### 6.3.1 页面权限控制

在页面级别添加权限检查：

```javascript
// pages/management/dashboard/dashboard.js
Page({
  onLoad: function(options) {
    // 检查页面访问权限
    if (!this.checkPagePermission()) {
      wx.showToast({
        title: '权限不足',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.initializePage();
  },

  checkPagePermission: function() {
    const app = getApp();
    const requiredRoles = ['admin', 'manager', 'platform_admin'];
    return app.checkPermission(requiredRoles);
  }
});
```

#### 6.3.2 组件权限控制

创建权限控制组件 `components/permission-check/permission-check.js`:

```javascript
Component({
  properties: {
    requiredRoles: {
      type: Array,
      value: []
    },
    requiredPermissions: {
      type: Array,
      value: []
    },
    fallbackText: {
      type: String,
      value: '权限不足'
    }
  },

  data: {
    hasPermission: false
  },

  lifetimes: {
    attached: function() {
      this.checkPermission();
    }
  },

  methods: {
    checkPermission: function() {
      const app = getApp();
      const userInfo = app.globalData.userInfo || {};
      
      let hasRolePermission = true;
      let hasSpecificPermission = true;

      // 检查角色权限
      if (this.properties.requiredRoles.length > 0) {
        hasRolePermission = this.properties.requiredRoles.includes(userInfo.role);
      }

      // 检查具体权限
      if (this.properties.requiredPermissions.length > 0) {
        const userPermissions = userInfo.permissions || [];
        hasSpecificPermission = this.properties.requiredPermissions.every(
          permission => userPermissions.includes(permission)
        );
      }

      this.setData({
        hasPermission: hasRolePermission && hasSpecificPermission
      });
    }
  }
});
```

---

## 第七部分: 生产环境上线

### 7.1 上线前检查清单

#### 7.1.1 功能完整性检查

```markdown
### 功能模块检查清单

#### ✅ 核心业务功能
- [ ] 用户注册登录功能正常
- [ ] 鹅群管理功能完整
- [ ] 健康监控功能运行正常
- [ ] 物料管理功能正常
- [ ] 商城购买流程完整
- [ ] 财务管理模块正常

#### ✅ 管理后台功能
- [ ] 管理员登录正常
- [ ] 租户管理功能完整
- [ ] 数据分析报表正常
- [ ] 系统监控功能运行
- [ ] 操作日志记录完整
- [ ] 权限控制严格

#### ✅ 数据安全性
- [ ] 租户数据完全隔离
- [ ] 用户权限控制严格
- [ ] 敏感数据加密存储
- [ ] API接口安全验证
- [ ] 操作日志完整记录

#### ✅ 性能优化
- [ ] 页面加载时间 < 2s
- [ ] 接口响应时间 < 500ms
- [ ] 图片资源优化完成
- [ ] 代码包大小 < 2MB
- [ ] 内存占用合理
```

#### 7.1.2 环境配置检查

```bash
# 生产环境配置验证脚本
#!/bin/bash

echo "=== 生产环境配置检查 ==="

# 检查环境变量
echo "1. 检查环境变量..."
if [ -f ".env.production" ]; then
    echo "✅ 生产环境配置文件存在"
    echo "配置内容:"
    cat .env.production | grep -v "PASSWORD\|SECRET\|KEY"
else
    echo "❌ 生产环境配置文件不存在"
    exit 1
fi

# 检查云开发环境
echo "2. 检查云开发环境..."
tcb env list --env your-prod-env-id
if [ $? -eq 0 ]; then
    echo "✅ 生产云开发环境正常"
else
    echo "❌ 生产云开发环境异常"
    exit 1
fi

# 检查云函数部署状态
echo "3. 检查云函数部署状态..."
FUNCTIONS=("login" "flockManagement" "adminManagement" "analytics")
for func in "${FUNCTIONS[@]}"; do
    tcb fn detail --name $func --env your-prod-env-id > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ $func 云函数部署正常"
    else
        echo "❌ $func 云函数部署异常"
    fi
done

echo "=== 检查完成 ==="
```

### 7.2 正式部署流程

#### 7.2.1 生产环境部署脚本

创建 `scripts/production-deploy.sh`:

```bash
#!/bin/bash

set -e

echo "=== 智慧养鹅SAAS平台生产环境部署 ==="

# 配置变量
PROD_ENV_ID="zhihuiyange-prod-xxx"
BACKUP_NAME="pre_deploy_$(date +%Y%m%d_%H%M%S)"

# 1. 数据备份
echo "1. 执行数据备份..."
tcb fn invoke --name dataBackup --env $PROD_ENV_ID --params '{
  "collections": ["users", "tenants", "flocks", "health_records"],
  "backup_name": "'$BACKUP_NAME'"
}'

# 2. 部署云函数
echo "2. 部署云函数..."
CLOUD_FUNCTIONS=(
    "auth/login"
    "auth/getUserInfo"
    "business/flockManagement"
    "business/healthManagement"
    "business/materialManagement"
    "business/shopManagement"
    "admin/adminManagement"
    "admin/systemManagement"
    "admin/tenantManagement"
    "admin/analytics"
    "admin/healthMonitoring"
    "admin/auditLog"
)

for func_path in "${CLOUD_FUNCTIONS[@]}"; do
    func_name=$(basename $func_path)
    echo "部署云函数: $func_name"
    
    cd "cloudfunctions/$func_path"
    npm install --production
    tcb fn deploy --name $func_name --env $PROD_ENV_ID
    cd - > /dev/null
    
    echo "✅ $func_name 部署完成"
done

# 3. 数据库结构更新
echo "3. 更新数据库结构..."
tcb fn invoke --name dbInit --env $PROD_ENV_ID --params '{
  "action": "createIndexes"
}'

# 4. 系统健康检查
echo "4. 执行系统健康检查..."
tcb fn invoke --name healthMonitoring --env $PROD_ENV_ID --params '{
  "action": "performHealthCheck"
}'

# 5. 生成部署报告
echo "5. 生成部署报告..."
cat > "deployment_report_$(date +%Y%m%d_%H%M%S).md" << EOF
# 生产环境部署报告

## 部署信息
- 部署时间: $(date)
- 环境ID: $PROD_ENV_ID
- 备份名称: $BACKUP_NAME
- 部署版本: v$(cat package.json | grep version | cut -d'"' -f4)

## 云函数部署状态
$(for func in "${CLOUD_FUNCTIONS[@]}"; do
    func_name=$(basename $func)
    echo "- $func_name: ✅ 部署成功"
done)

## 部署后检查
- [ ] 用户登录功能正常
- [ ] 核心业务功能正常
- [ ] 管理后台访问正常
- [ ] 系统监控正常

EOF

echo "=== 部署完成 ==="
echo "请执行部署后检查确认系统正常运行"
```

#### 7.2.2 部署后验证脚本

```bash
#!/bin/bash

echo "=== 生产环境部署验证 ==="

PROD_ENV_ID="zhihuiyange-prod-xxx"

# 1. 云函数状态检查
echo "1. 检查云函数状态..."
FUNCTIONS=("login" "flockManagement" "adminManagement" "analytics" "healthMonitoring")

for func in "${FUNCTIONS[@]}"; do
    status=$(tcb fn list --env $PROD_ENV_ID | grep $func | awk '{print $3}')
    if [ "$status" = "Active" ]; then
        echo "✅ $func: 正常运行"
    else
        echo "❌ $func: 状态异常 ($status)"
    fi
done

# 2. 数据库连接检查
echo "2. 检查数据库连接..."
result=$(tcb fn invoke --name healthMonitoring --env $PROD_ENV_ID --params '{
    "action": "performHealthCheck"
}' 2>/dev/null)

if echo "$result" | grep -q '"overall":"healthy"'; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接异常"
    echo "$result"
fi

# 3. API接口测试
echo "3. 执行API接口测试..."
# 这里添加具体的API测试逻辑

echo "=== 验证完成 ==="
```

### 7.3 域名配置

#### 7.3.1 微信小程序域名配置

在微信公众平台配置合法域名：

```
request合法域名:
- https://api.zhihuiyange.com
- https://tcb-api.tencentcloudapi.com

uploadFile合法域名:
- https://upload.zhihuiyange.com
- https://cos.ap-shanghai.myqcloud.com

downloadFile合法域名:
- https://static.zhihuiyange.com
- https://cos.ap-shanghai.myqcloud.com

socket合法域名:
- wss://ws.zhihuiyange.com
```

#### 7.3.2 CDN配置

配置CDN加速静态资源：

```nginx
# nginx配置示例
server {
    listen 443 ssl http2;
    server_name static.zhihuiyange.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location / {
        proxy_pass https://cos.ap-shanghai.myqcloud.com;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

---

## 第八部分: 监控与运维

### 8.1 系统监控配置

#### 8.1.1 云函数监控

创建系统监控云函数 `cloudfunctions/admin/systemMonitoring/index.js`:

```javascript
/**
 * 系统监控云函数
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 监控指标收集
exports.main = async (event, context) => {
  const { action = 'collectMetrics' } = event;
  
  try {
    switch (action) {
      case 'collectMetrics':
        return await collectSystemMetrics();
      case 'checkHealth':
        return await performHealthCheck();
      case 'generateReport':
        return await generateMonitoringReport();
      default:
        throw new Error('不支持的监控操作');
    }
  } catch (error) {
    console.error('系统监控失败:', error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

async function collectSystemMetrics() {
  const metrics = {
    timestamp: new Date(),
    
    // 云函数调用统计
    cloudFunctionStats: await getCloudFunctionStats(),
    
    // 数据库性能指标
    databaseMetrics: await getDatabaseMetrics(),
    
    // 存储使用情况
    storageMetrics: await getStorageMetrics(),
    
    // 用户活跃度统计
    userActivityMetrics: await getUserActivityMetrics()
  };

  // 保存监控数据
  await db.collection('system_metrics').add({
    data: metrics
  });

  // 检查告警阈值
  await checkAlertThresholds(metrics);

  return {
    success: true,
    metrics: metrics
  };
}

async function getCloudFunctionStats() {
  // 模拟云函数统计数据
  return {
    totalInvocations: Math.floor(Math.random() * 10000),
    avgResponseTime: Math.floor(Math.random() * 500) + 100,
    errorRate: Math.random() * 5,
    activeConnections: Math.floor(Math.random() * 100)
  };
}

async function getDatabaseMetrics() {
  const startTime = Date.now();
  
  // 执行数据库测试查询
  await db.collection('users').limit(1).get();
  
  const responseTime = Date.now() - startTime;
  
  // 获取数据库统计信息
  const collections = ['users', 'tenants', 'flocks', 'health_records'];
  const collectionStats = {};
  
  for (const collection of collections) {
    const count = await db.collection(collection).count();
    collectionStats[collection] = count.total;
  }
  
  return {
    responseTime: responseTime,
    collectionStats: collectionStats,
    totalRecords: Object.values(collectionStats).reduce((a, b) => a + b, 0)
  };
}

async function getUserActivityMetrics() {
  const now = new Date();
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  
  // 活跃用户统计
  const dailyActiveUsers = await db.collection('operation_logs')
    .where({
      timestamp: db.command.gte(oneDayAgo)
    })
    .field({
      user_id: true
    })
    .get();
    
  const weeklyActiveUsers = await db.collection('operation_logs')
    .where({
      timestamp: db.command.gte(oneWeekAgo)
    })
    .field({
      user_id: true
    })
    .get();

  return {
    dailyActiveUsers: new Set(dailyActiveUsers.data.map(log => log.user_id)).size,
    weeklyActiveUsers: new Set(weeklyActiveUsers.data.map(log => log.user_id)).size,
    totalUsers: (await db.collection('users').count()).total
  };
}

async function checkAlertThresholds(metrics) {
  const alerts = [];
  
  // 检查响应时间告警
  if (metrics.cloudFunctionStats.avgResponseTime > 1000) {
    alerts.push({
      type: 'performance',
      severity: 'warning',
      message: `云函数平均响应时间过长: ${metrics.cloudFunctionStats.avgResponseTime}ms`,
      threshold: 1000,
      current: metrics.cloudFunctionStats.avgResponseTime
    });
  }
  
  // 检查错误率告警
  if (metrics.cloudFunctionStats.errorRate > 5) {
    alerts.push({
      type: 'error_rate',
      severity: 'critical',
      message: `云函数错误率过高: ${metrics.cloudFunctionStats.errorRate}%`,
      threshold: 5,
      current: metrics.cloudFunctionStats.errorRate
    });
  }
  
  // 发送告警通知
  if (alerts.length > 0) {
    await sendAlertNotifications(alerts);
  }
  
  return alerts;
}

async function sendAlertNotifications(alerts) {
  for (const alert of alerts) {
    await db.collection('system_alerts').add({
      data: {
        ...alert,
        created_time: new Date(),
        status: 'active'
      }
    });
  }
  
  // 这里可以添加发送邮件、短信等通知逻辑
  console.log('系统告警:', alerts);
}
```

#### 8.1.2 定时监控任务配置

为监控云函数配置定时触发器：

```json
// cloudfunctions/admin/systemMonitoring/config.json
{
  "triggers": [
    {
      "name": "metricsCollection",
      "type": "timer",
      "config": "0 */5 * * * * *",
      "payload": {
        "action": "collectMetrics"
      }
    },
    {
      "name": "healthCheck", 
      "type": "timer",
      "config": "0 */10 * * * * *",
      "payload": {
        "action": "checkHealth"
      }
    }
  ]
}
```

### 8.2 日志管理

#### 8.2.1 日志收集配置

创建日志管理工具 `utils/production-logger.js`:

```javascript
/**
 * 生产环境日志管理
 */
class ProductionLogger {
  constructor() {
    this.logLevel = this.getLogLevel();
    this.enableRemoteLogging = true;
  }

  getLogLevel() {
    const app = getApp();
    return app.globalData.environment === 'production' ? 'warn' : 'debug';
  }

  debug(...args) {
    if (this.shouldLog('debug')) {
      console.log('[DEBUG]', ...args);
      this.sendToRemote('debug', args);
    }
  }

  info(...args) {
    if (this.shouldLog('info')) {
      console.info('[INFO]', ...args);
      this.sendToRemote('info', args);
    }
  }

  warn(...args) {
    if (this.shouldLog('warn')) {
      console.warn('[WARN]', ...args);
      this.sendToRemote('warn', args);
    }
  }

  error(...args) {
    if (this.shouldLog('error')) {
      console.error('[ERROR]', ...args);
      this.sendToRemote('error', args);
    }
  }

  shouldLog(level) {
    const levels = ['debug', 'info', 'warn', 'error'];
    return levels.indexOf(level) >= levels.indexOf(this.logLevel);
  }

  async sendToRemote(level, args) {
    if (!this.enableRemoteLogging || level === 'debug') return;

    try {
      await wx.cloud.callFunction({
        name: 'systemMonitoring',
        data: {
          action: 'logEvent',
          logData: {
            level: level,
            message: args.map(arg => 
              typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' '),
            timestamp: new Date().toISOString(),
            page: this.getCurrentPage(),
            userInfo: this.getUserInfo()
          }
        }
      });
    } catch (error) {
      console.error('远程日志发送失败:', error);
    }
  }

  getCurrentPage() {
    const pages = getCurrentPages();
    return pages.length > 0 ? pages[pages.length - 1].route : 'unknown';
  }

  getUserInfo() {
    const app = getApp();
    const userInfo = app.globalData.userInfo || {};
    return {
      id: userInfo.id,
      role: userInfo.role,
      tenant_id: userInfo.tenant_id
    };
  }
}

const logger = new ProductionLogger();

module.exports = logger;
```

### 8.3 性能优化监控

#### 8.3.1 性能监控配置

创建性能监控工具 `utils/performance-monitor.js`:

```javascript
/**
 * 性能监控工具
 */
class PerformanceMonitor {
  constructor() {
    this.performanceData = {};
    this.reportInterval = 30000; // 30秒上报一次
    this.startReporting();
  }

  // 开始页面性能监控
  startPagePerformance(pageName) {
    this.performanceData[pageName] = {
      startTime: Date.now(),
      pageName: pageName
    };
  }

  // 结束页面性能监控
  endPagePerformance(pageName) {
    if (this.performanceData[pageName]) {
      this.performanceData[pageName].loadTime = 
        Date.now() - this.performanceData[pageName].startTime;
      
      this.reportPagePerformance(pageName);
    }
  }

  // 监控API请求性能
  monitorAPICall(apiName, startTime, endTime, success) {
    const performanceData = {
      apiName: apiName,
      responseTime: endTime - startTime,
      success: success,
      timestamp: new Date().toISOString()
    };

    this.reportAPIPerformance(performanceData);
  }

  // 上报页面性能数据
  async reportPagePerformance(pageName) {
    const data = this.performanceData[pageName];
    if (!data) return;

    try {
      await wx.cloud.callFunction({
        name: 'systemMonitoring',
        data: {
          action: 'reportPerformance',
          performanceData: {
            type: 'page',
            pageName: pageName,
            loadTime: data.loadTime,
            timestamp: new Date().toISOString()
          }
        }
      });
    } catch (error) {
      console.error('性能数据上报失败:', error);
    }
  }

  // 上报API性能数据
  async reportAPIPerformance(performanceData) {
    try {
      await wx.cloud.callFunction({
        name: 'systemMonitoring',
        data: {
          action: 'reportPerformance',
          performanceData: {
            type: 'api',
            ...performanceData
          }
        }
      });
    } catch (error) {
      console.error('API性能数据上报失败:', error);
    }
  }

  // 定期上报系统性能
  startReporting() {
    setInterval(() => {
      this.reportSystemPerformance();
    }, this.reportInterval);
  }

  async reportSystemPerformance() {
    const systemInfo = wx.getSystemInfoSync();
    
    const performanceData = {
      type: 'system',
      memory: systemInfo.memory || {},
      storage: systemInfo.storage || {},
      network: systemInfo.networkType,
      timestamp: new Date().toISOString()
    };

    try {
      await wx.cloud.callFunction({
        name: 'systemMonitoring',
        data: {
          action: 'reportPerformance',
          performanceData: performanceData
        }
      });
    } catch (error) {
      console.error('系统性能数据上报失败:', error);
    }
  }
}

const performanceMonitor = new PerformanceMonitor();

module.exports = performanceMonitor;
```

---

## 第九部分: 安全与合规

### 9.1 数据安全配置

#### 9.1.1 数据加密策略

```javascript
/**
 * 数据加密工具
 * utils/data-encryption.js
 */
const crypto = require('crypto');

class DataEncryption {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.secretKey = this.getSecretKey();
  }

  getSecretKey() {
    // 从环境变量或安全配置中获取密钥
    return process.env.ENCRYPTION_KEY || 'default-secret-key-change-in-production';
  }

  // 加密敏感数据
  encrypt(text) {
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher(this.algorithm, this.secretKey);
      
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      return {
        iv: iv.toString('hex'),
        encryptedData: encrypted
      };
    } catch (error) {
      console.error('数据加密失败:', error);
      return null;
    }
  }

  // 解密敏感数据
  decrypt(encryptedData) {
    try {
      const decipher = crypto.createDecipher(this.algorithm, this.secretKey);
      
      let decrypted = decipher.update(encryptedData.encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      console.error('数据解密失败:', error);
      return null;
    }
  }

  // 哈希处理 (不可逆)
  hash(text) {
    return crypto.createHash('sha256').update(text).digest('hex');
  }

  // 验证哈希
  verifyHash(text, hash) {
    return this.hash(text) === hash;
  }
}

const dataEncryption = new DataEncryption();

module.exports = dataEncryption;
```

#### 9.1.2 安全配置检查

创建安全检查脚本 `scripts/security-audit.js`:

```javascript
/**
 * 安全审计脚本
 */
const fs = require('fs');
const path = require('path');

class SecurityAuditor {
  constructor() {
    this.securityIssues = [];
    this.securityRules = {
      // 敏感信息检查规则
      sensitivePatterns: [
        /password\s*[:=]\s*['"]\w+['"]/gi,
        /secret\s*[:=]\s*['"]\w+['"]/gi,
        /api_?key\s*[:=]\s*['"]\w+['"]/gi,
        /token\s*[:=]\s*['"]\w+['"]/gi
      ],
      
      // 不安全函数检查
      unsafeFunctions: [
        'eval(',
        'setTimeout(',
        'setInterval(',
        'Function('
      ],
      
      // 权限检查缺失
      missingPermissionCheck: [
        'wx.cloud.callFunction',
        'db.collection(',
        'secureQuery('
      ]
    };
  }

  async auditProject() {
    console.log('开始安全审计...');
    
    await this.scanFiles('./');
    await this.checkCloudFunctionSecurity();
    await this.checkDatabaseSecurity();
    
    this.generateSecurityReport();
  }

  async scanFiles(directory) {
    const files = fs.readdirSync(directory);
    
    for (const file of files) {
      const filePath = path.join(directory, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !this.shouldSkipDirectory(file)) {
        await this.scanFiles(filePath);
      } else if (stat.isFile() && this.shouldScanFile(file)) {
        await this.scanFile(filePath);
      }
    }
  }

  shouldSkipDirectory(dirName) {
    return ['node_modules', '.git', 'backup'].includes(dirName);
  }

  shouldScanFile(fileName) {
    return /\.(js|ts|json)$/.test(fileName);
  }

  async scanFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查敏感信息
      this.checkSensitiveData(filePath, content);
      
      // 检查不安全函数
      this.checkUnsafeFunctions(filePath, content);
      
      // 检查权限控制
      this.checkPermissionControls(filePath, content);
      
    } catch (error) {
      console.error(`扫描文件失败 ${filePath}:`, error.message);
    }
  }

  checkSensitiveData(filePath, content) {
    this.securityRules.sensitivePatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        this.securityIssues.push({
          type: 'sensitive_data',
          severity: 'high',
          file: filePath,
          issue: '发现敏感信息硬编码',
          details: matches.join(', '),
          recommendation: '将敏感信息移动到环境变量或安全配置文件中'
        });
      }
    });
  }

  checkUnsafeFunctions(filePath, content) {
    this.securityRules.unsafeFunctions.forEach(func => {
      if (content.includes(func)) {
        this.securityIssues.push({
          type: 'unsafe_function',
          severity: 'medium',
          file: filePath,
          issue: `使用了不安全函数: ${func}`,
          recommendation: '避免使用动态执行函数，使用更安全的替代方案'
        });
      }
    });
  }

  checkPermissionControls(filePath, content) {
    // 检查云函数调用是否有权限检查
    if (content.includes('wx.cloud.callFunction') && 
        !content.includes('checkPermission') &&
        !content.includes('hasPermission')) {
      this.securityIssues.push({
        type: 'missing_permission',
        severity: 'medium',
        file: filePath,
        issue: '云函数调用缺少权限检查',
        recommendation: '在调用云函数前添加权限验证'
      });
    }
  }

  generateSecurityReport() {
    const report = {
      scanTime: new Date().toISOString(),
      totalIssues: this.securityIssues.length,
      severityBreakdown: {
        high: this.securityIssues.filter(i => i.severity === 'high').length,
        medium: this.securityIssues.filter(i => i.severity === 'medium').length,
        low: this.securityIssues.filter(i => i.severity === 'low').length
      },
      issues: this.securityIssues
    };

    // 写入报告文件
    fs.writeFileSync(
      `security_report_${Date.now()}.json`, 
      JSON.stringify(report, null, 2)
    );

    console.log('=== 安全审计报告 ===');
    console.log(`总问题数: ${report.totalIssues}`);
    console.log(`高危问题: ${report.severityBreakdown.high}`);
    console.log(`中危问题: ${report.severityBreakdown.medium}`);
    console.log(`低危问题: ${report.severityBreakdown.low}`);

    if (report.severityBreakdown.high > 0) {
      console.log('⚠️  发现高危安全问题，请立即修复！');
    } else {
      console.log('✅ 未发现高危安全问题');
    }
  }
}

// 执行安全审计
const auditor = new SecurityAuditor();
auditor.auditProject();
```

### 9.2 合规性检查

#### 9.2.1 数据合规性验证

```javascript
/**
 * 数据合规性检查工具
 */
class DataComplianceChecker {
  constructor() {
    this.complianceRules = {
      // 个人数据保护规则
      personalDataFields: [
        'real_name', 'phone', 'id_card', 'address', 'email'
      ],
      
      // 敏感业务数据
      sensitiveBusinessData: [
        'financial_records', 'health_records', 'veterinary_records'
      ],
      
      // 租户隔离必需字段
      tenantIsolationFields: [
        'tenant_id'
      ]
    };
  }

  async checkDataCompliance() {
    console.log('开始数据合规性检查...');
    
    const results = {
      personalDataProtection: await this.checkPersonalDataProtection(),
      tenantIsolation: await this.checkTenantIsolation(),
      dataRetention: await this.checkDataRetention(),
      accessControl: await this.checkAccessControl()
    };

    this.generateComplianceReport(results);
    return results;
  }

  async checkPersonalDataProtection() {
    // 检查个人数据是否正确加密和保护
    const issues = [];
    
    // 这里添加具体的个人数据保护检查逻辑
    return {
      status: 'compliant',
      issues: issues,
      recommendations: []
    };
  }

  async checkTenantIsolation() {
    // 检查多租户数据隔离
    const issues = [];
    
    // 检查所有业务数据表是否包含 tenant_id 字段
    const businessCollections = ['flocks', 'health_records', 'materials', 'orders'];
    
    for (const collection of businessCollections) {
      // 这里应该连接数据库检查表结构
      // 为演示目的，假设都包含 tenant_id
    }
    
    return {
      status: 'compliant',
      issues: issues,
      recommendations: []
    };
  }

  generateComplianceReport(results) {
    const report = {
      checkTime: new Date().toISOString(),
      overallStatus: this.calculateOverallStatus(results),
      details: results,
      nextCheckDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
    };

    console.log('=== 数据合规性检查报告 ===');
    console.log(`总体状态: ${report.overallStatus}`);
    console.log(`检查时间: ${report.checkTime}`);
    console.log(`下次检查: ${report.nextCheckDate}`);
  }

  calculateOverallStatus(results) {
    const statuses = Object.values(results).map(r => r.status);
    return statuses.every(s => s === 'compliant') ? 'compliant' : 'non_compliant';
  }
}
```

---

## 第十部分: 故障排查手册

### 10.1 常见问题诊断

#### 10.1.1 云函数调用失败

**症状**: 小程序调用云函数时返回错误或超时

**可能原因**:
1. 云函数未正确部署
2. 环境ID配置错误
3. 权限配置问题
4. 云函数代码异常

**排查步骤**:

```bash
# 1. 检查云函数列表
tcb fn list --env your-env-id

# 2. 查看云函数详情
tcb fn detail --name function-name --env your-env-id

# 3. 查看云函数日志
tcb fn log --name function-name --env your-env-id --lines 100

# 4. 测试云函数调用
tcb fn invoke --name function-name --env your-env-id --params '{}'
```

**解决方案**:

```javascript
// 在小程序中添加错误处理
wx.cloud.callFunction({
  name: 'functionName',
  data: {},
  success: res => {
    console.log('调用成功:', res);
  },
  fail: err => {
    console.error('调用失败:', err);
    
    // 根据错误类型进行处理
    switch (err.errCode) {
      case -1:
        wx.showToast({ title: '网络错误，请重试', icon: 'none' });
        break;
      case -404002:
        wx.showToast({ title: '云函数不存在', icon: 'none' });
        break;
      default:
        wx.showToast({ title: '系统异常', icon: 'none' });
    }
  }
});
```

#### 10.1.2 数据库操作异常

**症状**: 数据库查询、插入、更新失败

**可能原因**:
1. 数据库权限不足
2. 数据格式错误
3. 索引缺失导致查询超时
4. 数据库连接数超限

**排查步骤**:

```javascript
// 数据库操作错误处理示例
async function databaseOperation() {
  try {
    const db = wx.cloud.database();
    
    // 添加详细的错误日志
    console.log('开始数据库操作...');
    
    const result = await db.collection('collection_name')
      .where({
        tenant_id: 'tenant_id',
        status: 'active'
      })
      .get();
    
    console.log('数据库操作成功:', result);
    return result;
    
  } catch (error) {
    console.error('数据库操作失败:', {
      error: error,
      errorCode: error.errCode,
      errorMessage: error.errMsg,
      stack: error.stack
    });
    
    // 根据错误类型进行处理
    if (error.errCode === -1) {
      throw new Error('网络连接失败，请检查网络');
    } else if (error.errCode === -100000) {
      throw new Error('数据库权限不足');
    } else {
      throw new Error('数据库操作异常');
    }
  }
}
```

#### 10.1.3 权限验证失败

**症状**: 用户无法访问某些功能或数据

**诊断脚本**:

```javascript
// 权限诊断工具
function diagnosePermissionIssue(userId, requiredPermission) {
  console.log('=== 权限诊断开始 ===');
  console.log(`用户ID: ${userId}`);
  console.log(`所需权限: ${requiredPermission}`);
  
  // 1. 检查用户信息
  const userInfo = wx.getStorageSync('user_info');
  console.log('用户信息:', userInfo);
  
  if (!userInfo) {
    console.error('❌ 用户未登录');
    return { success: false, reason: '用户未登录' };
  }
  
  // 2. 检查用户角色
  console.log(`用户角色: ${userInfo.role}`);
  
  // 3. 检查权限配置
  const { hasPermission } = require('../utils/role-permission.js');
  const hasRolePermission = hasPermission(userInfo.role, requiredPermission);
  console.log(`角色权限检查: ${hasRolePermission}`);
  
  // 4. 检查用户特定权限
  const userPermissions = userInfo.permissions || [];
  const hasUserPermission = userPermissions.includes(requiredPermission);
  console.log(`用户权限检查: ${hasUserPermission}`);
  
  console.log('=== 权限诊断结束 ===');
  
  return {
    success: hasRolePermission || hasUserPermission,
    userInfo: userInfo,
    hasRolePermission: hasRolePermission,
    hasUserPermission: hasUserPermission,
    recommendations: [
      !hasRolePermission && '需要更高级别的角色权限',
      !hasUserPermission && '需要添加特定用户权限'
    ].filter(Boolean)
  };
}
```

### 10.2 性能问题排查

#### 10.2.1 页面加载缓慢

**性能分析工具**:

```javascript
// 页面性能分析工具
class PagePerformanceAnalyzer {
  constructor(pageName) {
    this.pageName = pageName;
    this.startTime = Date.now();
    this.milestones = {};
  }

  milestone(name) {
    this.milestones[name] = Date.now() - this.startTime;
    console.log(`[${this.pageName}] ${name}: ${this.milestones[name]}ms`);
  }

  complete() {
    const totalTime = Date.now() - this.startTime;
    console.log(`[${this.pageName}] 总加载时间: ${totalTime}ms`);
    
    // 性能分析报告
    const report = {
      pageName: this.pageName,
      totalTime: totalTime,
      milestones: this.milestones,
      performance: this.analyzePerformance(totalTime)
    };

    this.sendPerformanceReport(report);
    return report;
  }

  analyzePerformance(totalTime) {
    if (totalTime < 1000) return 'excellent';
    if (totalTime < 2000) return 'good';
    if (totalTime < 3000) return 'fair';
    return 'poor';
  }

  sendPerformanceReport(report) {
    // 发送性能报告到监控系统
    wx.cloud.callFunction({
      name: 'systemMonitoring',
      data: {
        action: 'reportPerformance',
        performanceData: report
      }
    }).catch(err => {
      console.error('性能报告发送失败:', err);
    });
  }
}

// 使用示例
Page({
  onLoad: function() {
    this.perf = new PagePerformanceAnalyzer('home');
    
    this.loadData().then(() => {
      this.perf.milestone('数据加载完成');
    });
  },

  onReady: function() {
    this.perf.milestone('页面渲染完成');
    this.perf.complete();
  }
});
```

### 10.3 应急响应流程

#### 10.3.1 系统故障应急处理

```bash
#!/bin/bash
# emergency-response.sh - 系统故障应急响应脚本

echo "=== 智慧养鹅SAAS平台应急响应 ==="

# 1. 系统状态检查
echo "1. 执行系统状态检查..."
./scripts/system-health-check.sh

# 2. 云函数状态检查
echo "2. 检查云函数状态..."
CRITICAL_FUNCTIONS=("login" "flockManagement" "adminManagement")

for func in "${CRITICAL_FUNCTIONS[@]}"; do
    status=$(tcb fn detail --name $func --env $PROD_ENV_ID | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    if [ "$status" != "Active" ]; then
        echo "❌ 关键云函数异常: $func"
        # 尝试重新部署
        echo "尝试重新部署 $func..."
        cd "cloudfunctions/*/$func" 2>/dev/null || cd "cloudfunctions/$func"
        tcb fn deploy --name $func --env $PROD_ENV_ID
        cd - > /dev/null
    else
        echo "✅ $func 状态正常"
    fi
done

# 3. 数据库连接检查
echo "3. 检查数据库连接..."
result=$(tcb fn invoke --name healthMonitoring --env $PROD_ENV_ID --params '{"action":"performHealthCheck"}' 2>/dev/null)

if echo "$result" | grep -q '"overall":"healthy"'; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接异常"
    echo "执行数据库修复..."
    # 这里添加数据库修复逻辑
fi

# 4. 生成故障报告
echo "4. 生成故障报告..."
cat > "incident_report_$(date +%Y%m%d_%H%M%S).md" << EOF
# 系统故障应急响应报告

## 故障信息
- 发生时间: $(date)
- 响应人员: $(whoami)
- 影响范围: 待评估

## 处理措施
- 执行了系统健康检查
- 检查并修复了云函数状态
- 验证了数据库连接

## 后续行动
- [ ] 详细分析故障原因
- [ ] 制定预防措施
- [ ] 更新监控告警规则

EOF

echo "=== 应急响应完成 ==="
echo "故障报告已生成，请查看详细信息"
```

#### 10.3.2 数据恢复流程

```bash
#!/bin/bash
# data-recovery.sh - 数据恢复脚本

echo "=== 数据恢复流程 ==="

# 检查可用备份
echo "1. 检查可用备份..."
tcb storage list --env $PROD_ENV_ID | grep "backups/"

echo "请选择要恢复的备份文件ID:"
read BACKUP_FILE_ID

# 下载备份文件
echo "2. 下载备份文件..."
tcb storage download --cloud-path "$BACKUP_FILE_ID" --local-path "./temp_backup.json" --env $PROD_ENV_ID

if [ $? -ne 0 ]; then
    echo "❌ 备份文件下载失败"
    exit 1
fi

# 解析备份数据
echo "3. 解析备份数据..."
COLLECTIONS=$(jq -r 'keys[]' temp_backup.json)

echo "包含以下集合的备份:"
echo "$COLLECTIONS"

echo "确认要恢复这些数据吗? (y/N)"
read CONFIRM

if [ "$CONFIRM" != "y" ] && [ "$CONFIRM" != "Y" ]; then
    echo "恢复操作已取消"
    exit 0
fi

# 创建数据恢复云函数调用
echo "4. 开始数据恢复..."
tcb fn invoke --name dataRestore --env $PROD_ENV_ID --params "{
    \"backupFile\": \"$BACKUP_FILE_ID\",
    \"collections\": $(echo "$COLLECTIONS" | jq -R -s -c 'split("\n")[:-1]')
}"

echo "=== 数据恢复完成 ==="
```

---

## 结论

本部署指南涵盖了智慧养鹅SAAS平台从开发到生产环境的完整部署流程，基于Context7最佳实践和微信小程序云开发规范。按照本指南执行部署，可确保系统安全、稳定、高效运行。

### 关键成功因素

1. **严格按照环境配置**: 确保开发、测试、生产环境隔离
2. **完整的权限控制**: 实施多层权限验证和数据隔离
3. **全面的监控体系**: 实时监控系统健康和性能指标
4. **定期安全审计**: 持续检查和修复安全漏洞
5. **完善的备份策略**: 定期备份数据，确保业务连续性

### 部署检查清单

- [ ] 云开发环境配置完成
- [ ] 所有云函数部署成功
- [ ] 数据库结构和索引创建完成
- [ ] API接口映射配置正确
- [ ] 权限控制系统运行正常
- [ ] 监控告警系统激活
- [ ] 数据备份计划执行
- [ ] 安全审计通过
- [ ] 性能指标达标
- [ ] 故障应急预案就绪

遵循本指南，智慧养鹅SAAS平台将具备企业级的稳定性、安全性和可扩展性，为智慧养殖行业提供专业、可靠的数字化解决方案。

---

*文档版本: v1.0*  
*最后更新: 2025年8月30日*  
*维护团队: Claude Code AI Assistant*