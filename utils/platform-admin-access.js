/**
 * 平台管理员数据访问控制器
 * Platform Admin Data Access Controller
 * 
 * 专门处理平台管理员的跨租户数据访问权限
 * 确保平台管理员可以安全地访问所有必要的数据
 */

const { ROLES, PERMISSIONS, hasPermission } = require('./role-permission');

/**
 * 平台管理员数据访问级别
 */
const PLATFORM_ACCESS_LEVELS = {
  FULL_ACCESS: 'full_access',        // 完全访问权限
  READ_ONLY: 'read_only',           // 只读访问权限
  METADATA_ONLY: 'metadata_only',   // 仅元数据访问
  NO_ACCESS: 'no_access'            // 无访问权限
};

/**
 * 数据类型访问权限映射
 */
const DATA_TYPE_ACCESS_MAP = {
  // 平台级数据 - 完全访问
  'goose_prices': PLATFORM_ACCESS_LEVELS.FULL_ACCESS,
  'platform_announcements': PLATFORM_ACCESS_LEVELS.FULL_ACCESS,
  'knowledge_base': PLATFORM_ACCESS_LEVELS.FULL_ACCESS,
  'shop_products': PLATFORM_ACCESS_LEVELS.FULL_ACCESS,
  'tenants': PLATFORM_ACCESS_LEVELS.FULL_ACCESS,
  'ai_configs': PLATFORM_ACCESS_LEVELS.FULL_ACCESS,
  'system_configs': PLATFORM_ACCESS_LEVELS.FULL_ACCESS,
  
  // 租户业务数据 - 跨租户只读访问
  'flocks': PLATFORM_ACCESS_LEVELS.READ_ONLY,
  'materials': PLATFORM_ACCESS_LEVELS.READ_ONLY,
  'health_records': PLATFORM_ACCESS_LEVELS.READ_ONLY,
  'production_records': PLATFORM_ACCESS_LEVELS.READ_ONLY,
  
  // 财务数据 - 仅元数据访问（保护敏感信息）
  'finance_records': PLATFORM_ACCESS_LEVELS.METADATA_ONLY,
  'finance_reports': PLATFORM_ACCESS_LEVELS.METADATA_ONLY,
  
  // 用户数据 - 只读访问
  'users': PLATFORM_ACCESS_LEVELS.READ_ONLY,
  'user_profiles': PLATFORM_ACCESS_LEVELS.READ_ONLY,
  
  // 审计日志 - 完全访问
  'operation_logs': PLATFORM_ACCESS_LEVELS.FULL_ACCESS,
  'audit_logs': PLATFORM_ACCESS_LEVELS.FULL_ACCESS
};

/**
 * 敏感字段过滤配置
 */
const SENSITIVE_FIELDS_FILTER = {
  'finance_records': [
    'bank_account', 'payment_details', 'invoice_details'
  ],
  'users': [
    'password', 'phone', 'email', 'id_card'
  ],
  'health_records': [
    'veterinarian_notes', 'treatment_details'
  ]
};

class PlatformAdminAccessController {
  constructor() {
    this.accessCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  }

  /**
   * 检查平台管理员是否可以访问指定数据
   * @param {Object} user 用户对象
   * @param {string} dataType 数据类型
   * @param {string} operation 操作类型 (read/write/delete)
   * @param {Object} context 上下文信息
   * @returns {Promise<Object>} 访问权限结果
   */
  async checkAccess(user, dataType, operation = 'read', context = {}) {
    try {
      // 验证用户是否为平台管理员
      if (!this.isPlatformAdmin(user)) {
        return {
          allowed: false,
          reason: '用户不是平台管理员',
          accessLevel: PLATFORM_ACCESS_LEVELS.NO_ACCESS
        };
      }

      // 获取数据类型的访问级别
      const accessLevel = DATA_TYPE_ACCESS_MAP[dataType] || PLATFORM_ACCESS_LEVELS.NO_ACCESS;
      
      // 检查操作权限
      const operationAllowed = this.checkOperationPermission(accessLevel, operation);
      
      if (!operationAllowed) {
        return {
          allowed: false,
          reason: `访问级别 ${accessLevel} 不支持操作 ${operation}`,
          accessLevel
        };
      }

      // 构建数据过滤器
      const dataFilter = this.buildDataFilter(user, dataType, accessLevel, context);
      
      // 构建字段过滤器
      const fieldFilter = this.buildFieldFilter(dataType, accessLevel);

      return {
        allowed: true,
        accessLevel,
        dataFilter,
        fieldFilter,
        restrictions: this.getAccessRestrictions(dataType, accessLevel)
      };

    } catch (error) {
      console.error('平台管理员访问权限检查失败:', error);
      return {
        allowed: false,
        reason: '权限检查失败',
        accessLevel: PLATFORM_ACCESS_LEVELS.NO_ACCESS
      };
    }
  }

  /**
   * 验证是否为平台管理员
   * @param {Object} user 用户对象
   * @returns {boolean}
   */
  isPlatformAdmin(user) {
    return user.role === ROLES.PLATFORM_ADMIN || user.role === ROLES.SUPER_ADMIN;
  }

  /**
   * 检查操作权限
   * @param {string} accessLevel 访问级别
   * @param {string} operation 操作类型
   * @returns {boolean}
   */
  checkOperationPermission(accessLevel, operation) {
    const operationMatrix = {
      [PLATFORM_ACCESS_LEVELS.FULL_ACCESS]: ['read', 'write', 'delete'],
      [PLATFORM_ACCESS_LEVELS.READ_ONLY]: ['read'],
      [PLATFORM_ACCESS_LEVELS.METADATA_ONLY]: ['read'],
      [PLATFORM_ACCESS_LEVELS.NO_ACCESS]: []
    };

    return operationMatrix[accessLevel]?.includes(operation) || false;
  }

  /**
   * 构建数据过滤器
   * @param {Object} user 用户对象
   * @param {string} dataType 数据类型
   * @param {string} accessLevel 访问级别
   * @param {Object} context 上下文
   * @returns {Object}
   */
  buildDataFilter(user, dataType, accessLevel, context) {
    // 平台级数据无需过滤
    if (accessLevel === PLATFORM_ACCESS_LEVELS.FULL_ACCESS) {
      return {};
    }

    // 租户数据需要根据上下文过滤
    if (context.tenantId) {
      return { tenant_id: context.tenantId };
    }

    // 默认返回所有租户数据（平台管理员跨租户访问）
    return {};
  }

  /**
   * 构建字段过滤器
   * @param {string} dataType 数据类型
   * @param {string} accessLevel 访问级别
   * @returns {Object}
   */
  buildFieldFilter(dataType, accessLevel) {
    const filter = {
      include: [],
      exclude: []
    };

    // 元数据访问模式下，排除敏感字段
    if (accessLevel === PLATFORM_ACCESS_LEVELS.METADATA_ONLY) {
      const sensitiveFields = SENSITIVE_FIELDS_FILTER[dataType] || [];
      filter.exclude = sensitiveFields;
    }

    // 只读访问模式下，排除部分敏感字段
    if (accessLevel === PLATFORM_ACCESS_LEVELS.READ_ONLY) {
      const sensitiveFields = SENSITIVE_FIELDS_FILTER[dataType] || [];
      // 只排除最敏感的字段
      filter.exclude = sensitiveFields.filter(field => 
        field.includes('password') || field.includes('secret')
      );
    }

    return filter;
  }

  /**
   * 获取访问限制信息
   * @param {string} dataType 数据类型
   * @param {string} accessLevel 访问级别
   * @returns {Object}
   */
  getAccessRestrictions(dataType, accessLevel) {
    const restrictions = {
      canModify: false,
      canDelete: false,
      canExport: false,
      maxRecords: null,
      timeRange: null
    };

    switch (accessLevel) {
      case PLATFORM_ACCESS_LEVELS.FULL_ACCESS:
        restrictions.canModify = true;
        restrictions.canDelete = true;
        restrictions.canExport = true;
        break;
        
      case PLATFORM_ACCESS_LEVELS.READ_ONLY:
        restrictions.canExport = true;
        restrictions.maxRecords = 10000; // 限制查询记录数
        break;
        
      case PLATFORM_ACCESS_LEVELS.METADATA_ONLY:
        restrictions.canExport = false;
        restrictions.maxRecords = 1000;
        restrictions.timeRange = 30; // 限制30天内的数据
        break;
    }

    return restrictions;
  }

  /**
   * 应用数据过滤器到查询结果
   * @param {Array} data 原始数据
   * @param {Object} fieldFilter 字段过滤器
   * @returns {Array}
   */
  applyFieldFilter(data, fieldFilter) {
    if (!fieldFilter || (!fieldFilter.include.length && !fieldFilter.exclude.length)) {
      return data;
    }

    return data.map(item => {
      const filteredItem = { ...item };

      // 排除指定字段
      fieldFilter.exclude.forEach(field => {
        delete filteredItem[field];
      });

      // 如果指定了包含字段，只保留这些字段
      if (fieldFilter.include.length > 0) {
        const includedItem = {};
        fieldFilter.include.forEach(field => {
          if (filteredItem.hasOwnProperty(field)) {
            includedItem[field] = filteredItem[field];
          }
        });
        return includedItem;
      }

      return filteredItem;
    });
  }

  /**
   * 记录访问日志
   * @param {Object} user 用户对象
   * @param {string} dataType 数据类型
   * @param {string} operation 操作类型
   * @param {Object} result 访问结果
   */
  async logAccess(user, dataType, operation, result) {
    try {
      const logEntry = {
        user_id: user._id,
        user_role: user.role,
        data_type: dataType,
        operation,
        allowed: result.allowed,
        access_level: result.accessLevel,
        timestamp: new Date(),
        ip_address: user.ip_address || '',
        user_agent: user.user_agent || ''
      };

      // 这里可以调用日志记录服务
      console.log('平台管理员数据访问日志:', logEntry);
      
    } catch (error) {
      console.error('记录访问日志失败:', error);
    }
  }

  /**
   * 获取平台管理员可访问的数据类型列表
   * @param {Object} user 用户对象
   * @returns {Array}
   */
  getAccessibleDataTypes(user) {
    if (!this.isPlatformAdmin(user)) {
      return [];
    }

    return Object.keys(DATA_TYPE_ACCESS_MAP).map(dataType => ({
      dataType,
      accessLevel: DATA_TYPE_ACCESS_MAP[dataType],
      restrictions: this.getAccessRestrictions(dataType, DATA_TYPE_ACCESS_MAP[dataType])
    }));
  }
}

// 创建单例实例
const platformAdminAccessController = new PlatformAdminAccessController();

module.exports = {
  PLATFORM_ACCESS_LEVELS,
  DATA_TYPE_ACCESS_MAP,
  SENSITIVE_FIELDS_FILTER,
  PlatformAdminAccessController,
  platformAdminAccessController
};
