/**
 * HTTP请求封装 - 微信小程序版本
 * 基础请求功能，支持云开发和HTTP双模式
 */

const { API } = require('../constants/index.js');

/**
 * 基础请求函数
 * @param {Object} options 请求参数
 */
function request(options = {}) {
  const defaultOptions = {
    url: '',
    method: 'GET',
    data: {},
    header: {
      'Content-Type': 'application/json'
    },
    timeout: 10000,
    showLoading: false,
    loadingText: '加载中...',
    showError: true
  };

  const finalOptions = Object.assign({}, defaultOptions, options);

  return new Promise((resolve, reject) => {
    // 显示loading
    if (finalOptions.showLoading) {
      wx.showLoading({
        title: finalOptions.loadingText,
        mask: true
      });
    }

    // 发起请求
    wx.request({
      url: finalOptions.url,
      method: finalOptions.method,
      data: finalOptions.data,
      header: finalOptions.header,
      timeout: finalOptions.timeout,
      success: (res) => {
        // 隐藏loading
        if (finalOptions.showLoading) {
          wx.hideLoading();
        }

        // 处理响应
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          const error = new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`);
          if (finalOptions.showError) {
            wx.showToast({
              title: error.message,
              icon: 'none',
              duration: 2000
            });
          }
          reject(error);
        }
      },
      fail: (err) => {
        // 隐藏loading
        if (finalOptions.showLoading) {
          wx.hideLoading();
        }

        const error = new Error(err.errMsg || '网络请求失败');
        if (finalOptions.showError) {
          wx.showToast({
            title: '网络请求失败',
            icon: 'none',
            duration: 2000
          });
        }
        reject(error);
      }
    });
  });
}

/**
 * GET请求
 */
function get(url, data = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  });
}

/**
 * POST请求
 */
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  });
}

/**
 * PUT请求
 */
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  });
}

/**
 * DELETE请求
 */
function del(url, data = {}, options = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  });
}

module.exports = {
  request,
  get,
  post,
  put,
  delete: del
};
