/**
 * HTTP请求封装 - 简化版本
 * 基于废弃版本重构，保持API兼容性
 */

const { API } = require('../constants/index.js');

/**
 * 基础请求函数
 * @param {Object} options 请求参数
 */
function request(options = {}) {
  const defaultOptions = {
    url: '',
    method: 'GET',
    data: {},
    header: {
      'Content-Type': 'application/json'
    },
    timeout: 10000,
    showLoading: false,
    loadingText: '加载中...',
    showError: true
  };

  const finalOptions = Object.assign({}, defaultOptions, options);

  return new Promise((resolve, reject) => {
    // 显示loading
    if (finalOptions.showLoading) {
      wx.showLoading({
        title: finalOptions.loadingText,
        mask: true
      });
    }

    wx.request({
      url: finalOptions.url,
      method: finalOptions.method,
      data: finalOptions.data,
      header: finalOptions.header,
      timeout: finalOptions.timeout,
      success: (res) => {
        if (finalOptions.showLoading) {
          wx.hideLoading();
        }

        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          const error = new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`);
          error.statusCode = res.statusCode;
          error.data = res.data;
          
          if (finalOptions.showError) {
            wx.showToast({
              title: error.message,
              icon: 'error'
            });
          }
          
          reject(error);
        }
      },
      fail: (err) => {
        if (finalOptions.showLoading) {
          wx.hideLoading();
        }

        if (finalOptions.showError) {
          wx.showToast({
            title: '网络请求失败',
            icon: 'error'
          });
        }

        reject(err);
      }
    });
  });
}

/**
 * GET请求
 */
function get(url, data = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  });
}

/**
 * POST请求
 */
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  });
}

/**
 * PUT请求
 */
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  });
}

/**
 * DELETE请求
 */
function del(url, options = {}) {
  return request({
    url,
    method: 'DELETE',
    ...options
  });
}

/**
 * 显示loading
 */
function showLoading(title = '加载中...') {
  wx.showLoading({
    title,
    mask: true
  });
}

/**
 * 隐藏loading
 */
function hideLoading() {
  wx.hideLoading();
}

/**
 * 显示错误提示
 */
function showError(message) {
  wx.showToast({
    title: message,
    icon: 'error'
  });
}

/**
 * 显示成功提示
 */
function showSuccess(message) {
  wx.showToast({
    title: message,
    icon: 'success'
  });
}

/**
 * 智能请求函数 - 兼容性方法
 */
function smartRequest(urlOrOptions, options = {}) {
  if (typeof urlOrOptions === 'string') {
    return request({
      url: urlOrOptions,
      ...options
    });
  } else {
    return request(urlOrOptions);
  }
}

/**
 * 创建安全请求 - 兼容性方法
 */
function createSafeRequest(defaultOptions = {}) {
  return function(options = {}) {
    return request({
      ...defaultOptions,
      ...options
    });
  };
}

module.exports = {
  // 基础请求方法
  request,
  get,
  post,
  put,
  del,

  // 智能请求方法
  smartRequest,
  createSafeRequest,

  // 工具函数
  showLoading,
  hideLoading,
  showError,
  showSuccess,

  // 常量（为了向下兼容）
  ERROR_TYPES: API?.ERROR_TYPES || {},
  ERROR_MESSAGES: API?.ERROR_MESSAGES || {}
};
