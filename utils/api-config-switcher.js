/**
 * API配置切换器
 * API Configuration Switcher
 * 
 * 支持多环境配置切换，智能API调用和模拟数据回退
 */

const request = require('./request.js');

// 配置模式枚举
const CONFIG_MODE = {
  DEVELOPMENT: 'development',  // 开发环境API
  MOCK: 'mock',               // 模拟数据模式（默认）
  PRODUCTION: 'production'    // 生产环境API
};

// 当前配置模式（默认使用模拟数据）
let currentMode = CONFIG_MODE.MOCK;

// 配置存储键
const CONFIG_STORAGE_KEY = 'api_config_mode';

/**
 * 初始化配置
 */
function initConfig() {
  try {
    const savedMode = wx.getStorageSync(CONFIG_STORAGE_KEY);
    if (savedMode && CONFIG_MODE[savedMode.toUpperCase()]) {
      currentMode = savedMode;
    }
  } catch (error) {
    console.warn('读取API配置失败，使用默认配置:', error);
  }
}

/**
 * 切换配置模式
 * @param {string} mode 配置模式
 */
function switchConfigMode(mode) {
  if (!CONFIG_MODE[mode.toUpperCase()]) {
    console.error('无效的配置模式:', mode);
    return false;
  }

  currentMode = mode;
  
  try {
    wx.setStorageSync(CONFIG_STORAGE_KEY, mode);
    console.log('API配置已切换到:', mode);
    return true;
  } catch (error) {
    console.error('保存API配置失败:', error);
    return false;
  }
}

/**
 * 获取当前配置模式
 */
function getCurrentMode() {
  return currentMode;
}

/**
 * 检查是否应该使用模拟数据
 */
function shouldUseMockData() {
  return currentMode === CONFIG_MODE.MOCK;
}

/**
 * 智能API调用
 * @param {string} endpoint API端点
 * @param {Function} mockDataProvider 模拟数据提供函数
 * @param {Object} options 请求选项
 */
async function smartAPICall(endpoint, mockDataProvider, options = {}) {
  const {
    method = 'GET',
    data = {},
    mockDelay = 300,
    timeout = 5000
  } = options;

  // 如果配置为模拟数据模式，直接返回模拟数据
  if (shouldUseMockData()) {
    console.log(`使用模拟数据: ${endpoint}`);
    
    // 模拟网络延迟
    if (mockDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, mockDelay));
    }
    
    const mockData = typeof mockDataProvider === 'function' 
      ? mockDataProvider() 
      : mockDataProvider;
    
    return {
      success: true,
      data: mockData,
      source: 'mock',
      message: '模拟数据'
    };
  }

  // 尝试真实API调用
  try {
    console.log(`尝试API调用: ${method} ${endpoint}`);
    
    let result;
    switch (method.toUpperCase()) {
      case 'GET':
        result = await request.get(endpoint, data);
        break;
      case 'POST':
        result = await request.post(endpoint, data);
        break;
      case 'PUT':
        result = await request.put(endpoint, data);
        break;
      case 'DELETE':
        result = await request.del(endpoint);
        break;
      default:
        throw new Error(`不支持的HTTP方法: ${method}`);
    }

    return {
      success: true,
      data: result,
      source: 'api',
      message: 'API调用成功'
    };

  } catch (error) {
    console.warn(`API调用失败，回退到模拟数据: ${endpoint}`, error);
    
    // 回退到模拟数据
    if (mockDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, mockDelay));
    }
    
    const mockData = typeof mockDataProvider === 'function' 
      ? mockDataProvider() 
      : mockDataProvider;
    
    return {
      success: true,
      data: mockData,
      source: 'mock_fallback',
      message: 'API调用失败，使用模拟数据',
      originalError: error
    };
  }
}

/**
 * 显示当前配置状态
 */
function showConfigStatus() {
  const status = {
    mode: currentMode,
    useMockData: shouldUseMockData(),
    description: getConfigDescription()
  };
  
  console.log('API配置状态:', status);
  return status;
}

/**
 * 获取配置描述
 */
function getConfigDescription() {
  const descriptions = {
    [CONFIG_MODE.DEVELOPMENT]: '开发环境 - 使用本地API服务',
    [CONFIG_MODE.MOCK]: '模拟数据模式 - 使用本地模拟数据',
    [CONFIG_MODE.PRODUCTION]: '生产环境 - 使用生产API服务'
  };
  
  return descriptions[currentMode] || '未知配置';
}

/**
 * 检查API可用性
 * @param {string} endpoint 测试端点
 */
async function checkAPIAvailability(endpoint = '/health') {
  if (shouldUseMockData()) {
    return { available: false, reason: '当前使用模拟数据模式' };
  }

  try {
    await request.get(endpoint);
    return { available: true, reason: 'API服务正常' };
  } catch (error) {
    return { 
      available: false, 
      reason: `API服务不可用: ${error.message}` 
    };
  }
}

/**
 * 批量API调用
 * @param {Array} calls API调用配置数组
 */
async function batchAPICall(calls) {
  const results = [];
  
  for (const call of calls) {
    try {
      const result = await smartAPICall(
        call.endpoint,
        call.mockDataProvider,
        call.options
      );
      results.push({
        endpoint: call.endpoint,
        success: true,
        ...result
      });
    } catch (error) {
      results.push({
        endpoint: call.endpoint,
        success: false,
        error: error.message
      });
    }
  }
  
  return results;
}

// 初始化配置
initConfig();

// 导出接口
module.exports = {
  CONFIG_MODE,
  switchConfigMode,
  getCurrentMode,
  shouldUseMockData,
  smartAPICall,
  showConfigStatus,
  getConfigDescription,
  checkAPIAvailability,
  batchAPICall
};
