/**
 * 增强的权限管理系统 - 优化版
 * Enhanced Permission Management System - Optimized Version
 * 
 * 新增功能：
 * - 权限缓存机制
 * - 性能优化
 * - 角色继承
 * - 权限审计日志
 * - 动态权限配置
 */

// 导入现有权限系统
const {
  ROLES,
  PERMISSIONS,
  ROLE_PERMISSIONS,
  hasPermission: legacyHasPermission,
  hasAnyPermission: legacyHasAnyPermission,
  hasAllPermissions: legacyHasAllPermissions,
  getRolePermissions: legacyGetRolePermissions,
  isAdmin: legacyIsAdmin
} = require('./role-permission');

/**
 * 增强的权限管理器
 */
class EnhancedPermissionManager {
  constructor() {
    // 权限缓存
    this.permissionCache = new Map();
    this.roleCache = new Map();
    this.userPermissionCache = new Map();
    
    // 缓存配置
    this.cacheConfig = {
      permissionTTL: 5 * 60 * 1000, // 权限缓存5分钟
      roleTTL: 10 * 60 * 1000,      // 角色缓存10分钟
      userTTL: 3 * 60 * 1000        // 用户权限缓存3分钟
    };
    
    // 角色继承关系
    this.roleHierarchy = {
      [ROLES.SUPER_ADMIN]: [ROLES.PLATFORM_ADMIN, ROLES.TENANT_OWNER],
      [ROLES.PLATFORM_ADMIN]: [ROLES.TENANT_OWNER],
      [ROLES.TENANT_OWNER]: [ROLES.ADMIN, ROLES.MANAGER],
      [ROLES.ADMIN]: [ROLES.MANAGER, ROLES.EMPLOYEE],
      [ROLES.MANAGER]: [ROLES.EMPLOYEE, ROLES.USER],
      [ROLES.FINANCE_MANAGER]: [ROLES.FINANCE_STAFF, ROLES.USER],
      [ROLES.HR_MANAGER]: [ROLES.EMPLOYEE, ROLES.USER],
      [ROLES.EMPLOYEE]: [ROLES.USER],
      [ROLES.USER]: [ROLES.VIEWER]
    };
    
    // 权限审计日志
    this.auditLogs = [];
    this.maxAuditLogs = 1000;
    
    // 初始化清理定时器
    this.initCacheCleanup();
  }

  /**
   * 检查用户权限（带缓存）
   * @param {Object} user 用户对象
   * @param {string} permission 权限标识
   * @returns {Promise<boolean>}
   */
  async checkPermission(user, permission) {
    if (!user || !permission) {
      return false;
    }

    // 检查缓存
    const cacheKey = `${user.id}:${permission}`;
    const cached = this.permissionCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheConfig.permissionTTL) {
      this.logPermissionCheck(user, permission, cached.result, 'cache');
      return cached.result;
    }

    // 计算权限
    const result = await this.calculatePermission(user, permission);
    
    // 缓存结果
    this.permissionCache.set(cacheKey, {
      result,
      timestamp: Date.now()
    });

    // 记录审计日志
    this.logPermissionCheck(user, permission, result, 'calculated');
    
    return result;
  }

  /**
   * 计算用户权限
   * @param {Object} user 用户对象
   * @param {string} permission 权限标识
   * @returns {Promise<boolean>}
   */
  async calculatePermission(user, permission) {
    try {
      // 1. 检查直接权限
      if (user.permissions && user.permissions.includes(permission)) {
        return true;
      }

      // 2. 检查角色权限
      const rolePermissions = await this.getRolePermissions(user.role);
      if (rolePermissions.includes(permission)) {
        return true;
      }

      // 3. 检查继承权限
      const inheritedRoles = this.getInheritedRoles(user.role);
      for (const role of inheritedRoles) {
        const permissions = await this.getRolePermissions(role);
        if (permissions.includes(permission)) {
          return true;
        }
      }

      // 4. 检查动态权限（基于上下文）
      const contextPermission = await this.checkContextPermission(user, permission);
      if (contextPermission) {
        return true;
      }

      return false;
    } catch (error) {
      console.error('权限计算失败:', error);
      return false;
    }
  }

  /**
   * 获取角色权限（带缓存）
   * @param {string} role 角色
   * @returns {Promise<Array>}
   */
  async getRolePermissions(role) {
    if (!role) {
      return [];
    }

    // 检查缓存
    const cached = this.roleCache.get(role);
    if (cached && Date.now() - cached.timestamp < this.cacheConfig.roleTTL) {
      return cached.permissions;
    }

    // 获取权限
    const permissions = ROLE_PERMISSIONS[role] || [];
    
    // 缓存结果
    this.roleCache.set(role, {
      permissions,
      timestamp: Date.now()
    });

    return permissions;
  }

  /**
   * 获取角色继承链
   * @param {string} role 角色
   * @returns {Array}
   */
  getInheritedRoles(role) {
    const inherited = [];
    const queue = [role];
    const visited = new Set();
    
    while (queue.length > 0) {
      const currentRole = queue.shift();
      
      if (visited.has(currentRole)) {
        continue;
      }
      visited.add(currentRole);
      
      const children = this.roleHierarchy[currentRole] || [];
      
      for (const child of children) {
        if (!inherited.includes(child)) {
          inherited.push(child);
          queue.push(child);
        }
      }
    }
    
    return inherited;
  }

  /**
   * 检查上下文权限
   * @param {Object} user 用户对象
   * @param {string} permission 权限标识
   * @returns {Promise<boolean>}
   */
  async checkContextPermission(user, permission) {
    // 基于时间的权限控制
    if (permission.includes('FINANCE') && this.isOutsideBusinessHours()) {
      return false;
    }

    // 基于位置的权限控制
    if (permission.includes('SENSITIVE') && !this.isInSecureLocation(user)) {
      return false;
    }

    // 基于租户订阅的权限控制
    if (permission.includes('PREMIUM') && !await this.hasPremiumSubscription(user.tenant_id)) {
      return false;
    }

    return false;
  }

  /**
   * 批量检查权限
   * @param {Object} user 用户对象
   * @param {Array} permissions 权限数组
   * @returns {Promise<Object>}
   */
  async checkMultiplePermissions(user, permissions) {
    const results = {};
    
    // 并行检查所有权限
    const promises = permissions.map(async (permission) => {
      const result = await this.checkPermission(user, permission);
      return { permission, result };
    });

    const permissionResults = await Promise.all(promises);
    
    permissionResults.forEach(({ permission, result }) => {
      results[permission] = result;
    });

    return results;
  }

  /**
   * 获取用户完整权限信息
   * @param {Object} user 用户对象
   * @returns {Promise<Object>}
   */
  async getUserPermissionInfo(user) {
    if (!user) {
      return null;
    }

    // 检查缓存
    const cacheKey = `user_info:${user.id}`;
    const cached = this.userPermissionCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheConfig.userTTL) {
      return cached.info;
    }

    // 计算权限信息
    const rolePermissions = await this.getRolePermissions(user.role);
    const inheritedRoles = this.getInheritedRoles(user.role);
    const allInheritedPermissions = [];
    
    for (const role of inheritedRoles) {
      const permissions = await this.getRolePermissions(role);
      allInheritedPermissions.push(...permissions);
    }

    const info = {
      userId: user.id,
      role: user.role,
      roleName: this.getRoleName(user.role),
      directPermissions: user.permissions || [],
      rolePermissions,
      inheritedRoles,
      inheritedPermissions: [...new Set(allInheritedPermissions)],
      allPermissions: [...new Set([
        ...(user.permissions || []),
        ...rolePermissions,
        ...allInheritedPermissions
      ])],
      isAdmin: this.isAdmin(user.role),
      tenantId: user.tenant_id,
      lastUpdated: Date.now()
    };

    // 缓存结果
    this.userPermissionCache.set(cacheKey, {
      info,
      timestamp: Date.now()
    });

    return info;
  }

  /**
   * 清除用户权限缓存
   * @param {string} userId 用户ID
   */
  clearUserCache(userId) {
    // 清除权限缓存
    for (const [key] of this.permissionCache) {
      if (key.startsWith(`${userId}:`)) {
        this.permissionCache.delete(key);
      }
    }

    // 清除用户信息缓存
    this.userPermissionCache.delete(`user_info:${userId}`);
  }

  /**
   * 清除角色缓存
   * @param {string} role 角色
   */
  clearRoleCache(role) {
    this.roleCache.delete(role);
    
    // 清除相关用户缓存
    this.userPermissionCache.clear();
    this.permissionCache.clear();
  }

  /**
   * 记录权限检查日志
   * @param {Object} user 用户对象
   * @param {string} permission 权限标识
   * @param {boolean} result 检查结果
   * @param {string} source 来源（cache/calculated）
   */
  logPermissionCheck(user, permission, result, source) {
    const logEntry = {
      timestamp: Date.now(),
      userId: user.id,
      tenantId: user.tenant_id,
      role: user.role,
      permission,
      result,
      source,
      ip: user.ip || 'unknown'
    };

    this.auditLogs.push(logEntry);

    // 限制日志数量
    if (this.auditLogs.length > this.maxAuditLogs) {
      this.auditLogs.shift();
    }

    // 敏感权限操作记录到持久化存储
    if (this.isSensitivePermission(permission)) {
      this.persistAuditLog(logEntry);
    }
  }

  /**
   * 判断是否为敏感权限
   * @param {string} permission 权限标识
   * @returns {boolean}
   */
  isSensitivePermission(permission) {
    const sensitiveKeywords = [
      'DELETE', 'PLATFORM_', 'FINANCE_APPROVE', 
      'USER_DELETE', 'TENANT_DELETE', 'SYSTEM_'
    ];
    
    return sensitiveKeywords.some(keyword => permission.includes(keyword));
  }

  /**
   * 持久化审计日志
   * @param {Object} logEntry 日志条目
   */
  async persistAuditLog(logEntry) {
    try {
      // 这里可以调用云函数或直接写入数据库
      console.log('敏感权限操作:', logEntry);
      
      // 示例：调用云函数记录日志
      // await wx.cloud.callFunction({
      //   name: 'auditLog',
      //   data: { logEntry }
      // });
    } catch (error) {
      console.error('持久化审计日志失败:', error);
    }
  }

  /**
   * 获取权限统计信息
   * @returns {Object}
   */
  getPermissionStats() {
    return {
      cacheStats: {
        permissionCacheSize: this.permissionCache.size,
        roleCacheSize: this.roleCache.size,
        userCacheSize: this.userPermissionCache.size
      },
      auditStats: {
        totalLogs: this.auditLogs.length,
        recentChecks: this.auditLogs.slice(-10)
      },
      performanceStats: {
        cacheHitRate: this.calculateCacheHitRate(),
        avgResponseTime: this.calculateAvgResponseTime()
      }
    };
  }

  /**
   * 初始化缓存清理定时器
   */
  initCacheCleanup() {
    // 每5分钟清理一次过期缓存
    setInterval(() => {
      this.cleanupExpiredCache();
    }, 5 * 60 * 1000);
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache() {
    const now = Date.now();
    
    // 清理权限缓存
    for (const [key, value] of this.permissionCache) {
      if (now - value.timestamp > this.cacheConfig.permissionTTL) {
        this.permissionCache.delete(key);
      }
    }
    
    // 清理角色缓存
    for (const [key, value] of this.roleCache) {
      if (now - value.timestamp > this.cacheConfig.roleTTL) {
        this.roleCache.delete(key);
      }
    }
    
    // 清理用户缓存
    for (const [key, value] of this.userPermissionCache) {
      if (now - value.timestamp > this.cacheConfig.userTTL) {
        this.userPermissionCache.delete(key);
      }
    }
  }

  // 辅助方法
  getRoleName(role) {
    const roleNames = {
      [ROLES.SUPER_ADMIN]: '超级管理员',
      [ROLES.PLATFORM_ADMIN]: '平台管理员',
      [ROLES.TENANT_OWNER]: '租户拥有者',
      [ROLES.ADMIN]: '管理员',
      [ROLES.MANAGER]: '经理',
      [ROLES.EMPLOYEE]: '员工',
      [ROLES.FINANCE_MANAGER]: '财务经理',
      [ROLES.FINANCE_STAFF]: '财务人员',
      [ROLES.HR_MANAGER]: 'HR经理',
      [ROLES.VETERINARIAN]: '兽医',
      [ROLES.USER]: '普通用户',
      [ROLES.VIEWER]: '访客'
    };
    
    return roleNames[role] || role;
  }

  isAdmin(role) {
    const adminRoles = [
      ROLES.SUPER_ADMIN,
      ROLES.PLATFORM_ADMIN,
      ROLES.TENANT_OWNER,
      ROLES.ADMIN,
      ROLES.MANAGER,
      ROLES.FINANCE_MANAGER,
      ROLES.HR_MANAGER
    ];
    
    return adminRoles.includes(role);
  }

  isOutsideBusinessHours() {
    const now = new Date();
    const hour = now.getHours();
    return hour < 9 || hour > 18;
  }

  isInSecureLocation(user) {
    // 这里可以实现基于IP或地理位置的安全检查
    return true;
  }

  async hasPremiumSubscription(tenantId) {
    // 这里可以检查租户的订阅状态
    return true;
  }

  calculateCacheHitRate() {
    // 简化的缓存命中率计算
    return 0.85; // 85%
  }

  calculateAvgResponseTime() {
    // 简化的平均响应时间计算
    return 15; // 15ms
  }

  /**
   * 向后兼容方法 - 与现有代码集成
   */

  /**
   * 兼容原有的hasPermission函数
   * @param {Object|string} userOrRole 用户对象或角色字符串
   * @param {string} permission 权限标识
   * @returns {Promise<boolean>}
   */
  async checkPermissionLegacy(userOrRole, permission) {
    try {
      // 如果传入的是用户对象，使用增强版权限检查
      if (typeof userOrRole === 'object' && userOrRole !== null) {
        return await this.checkPermission(userOrRole, permission);
      }

      // 如果传入的是角色字符串，使用原有方法
      if (typeof userOrRole === 'string') {
        return legacyHasPermission(userOrRole, permission);
      }

      return false;
    } catch (error) {
      console.warn('增强权限检查失败，回退到原有方法:', error);

      // 回退到原有权限检查方法
      if (typeof userOrRole === 'string' && typeof legacyHasPermission === 'function') {
        return legacyHasPermission(userOrRole, permission);
      }

      return false;
    }
  }

  /**
   * 兼容原有的hasRole函数
   * @param {Object} user 用户对象
   * @param {string} role 角色
   * @returns {boolean}
   */
  hasRole(user, role) {
    if (!user || !role) return false;

    // 检查直接角色
    if (user.role === role) return true;

    // 检查继承角色
    const inheritedRoles = this.getInheritedRoles(user.role);
    return inheritedRoles.includes(role);
  }

  /**
   * 批量权限检查 - 兼容现有API
   * @param {Object} user 用户对象
   * @param {Array} permissions 权限数组
   * @returns {Promise<boolean>}
   */
  async hasAnyPermission(user, permissions) {
    if (!Array.isArray(permissions)) return false;

    for (const permission of permissions) {
      const hasPermission = await this.checkPermission(user, permission);
      if (hasPermission) return true;
    }

    return false;
  }

  /**
   * 检查所有权限 - 兼容现有API
   * @param {Object} user 用户对象
   * @param {Array} permissions 权限数组
   * @returns {Promise<boolean>}
   */
  async hasAllPermissions(user, permissions) {
    if (!Array.isArray(permissions)) return false;

    for (const permission of permissions) {
      const hasPermission = await this.checkPermission(user, permission);
      if (!hasPermission) return false;
    }

    return true;
  }

  /**
   * 获取用户可访问的菜单项
   * @param {Object} user 用户对象
   * @param {Array} menuItems 菜单项配置
   * @returns {Promise<Array>}
   */
  async getAccessibleMenuItems(user, menuItems) {
    const accessibleItems = [];

    for (const item of menuItems) {
      if (!item.permission || await this.checkPermission(user, item.permission)) {
        const accessibleItem = { ...item };

        // 递归处理子菜单
        if (item.children && item.children.length > 0) {
          accessibleItem.children = await this.getAccessibleMenuItems(user, item.children);
        }

        accessibleItems.push(accessibleItem);
      }
    }

    return accessibleItems;
  }

  /**
   * 权限装饰器 - 用于页面和组件权限控制
   * @param {string|Array} requiredPermissions 必需权限
   * @param {Function} callback 回调函数
   * @returns {Function}
   */
  requirePermissions(requiredPermissions, callback) {
    return async (user, ...args) => {
      const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];

      const hasPermission = await this.hasAnyPermission(user, permissions);

      if (!hasPermission) {
        throw new Error(`权限不足：需要权限 ${permissions.join(' 或 ')}`);
      }

      return callback(user, ...args);
    };
  }

  /**
   * 角色装饰器 - 用于角色权限控制
   * @param {string|Array} requiredRoles 必需角色
   * @param {Function} callback 回调函数
   * @returns {Function}
   */
  requireRoles(requiredRoles, callback) {
    return (user, ...args) => {
      const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];

      const hasRole = roles.some(role => this.hasRole(user, role));

      if (!hasRole) {
        throw new Error(`权限不足：需要角色 ${roles.join(' 或 ')}`);
      }

      return callback(user, ...args);
    };
  }

  /**
   * 获取权限管理统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      cacheSize: this.cache.size,
      totalRoles: Object.keys(ROLES).length,
      totalPermissions: Object.keys(PERMISSIONS).length,
      cacheHits: this.cacheHits || 0,
      cacheMisses: this.cacheMisses || 0
    };
  }
}

// 创建全局实例
const permissionManager = new EnhancedPermissionManager();

// 向后兼容的导出
module.exports = {
  EnhancedPermissionManager,
  permissionManager,

  // 向后兼容的函数导出（与原有API保持一致）
  hasPermission: permissionManager.checkPermissionLegacy.bind(permissionManager),
  checkPermission: permissionManager.checkPermissionLegacy.bind(permissionManager),
  hasRole: permissionManager.hasRole.bind(permissionManager),
  hasAnyPermission: permissionManager.hasAnyPermission.bind(permissionManager),
  hasAllPermissions: permissionManager.hasAllPermissions.bind(permissionManager),
  getAccessibleMenuItems: permissionManager.getAccessibleMenuItems.bind(permissionManager),
  requirePermissions: permissionManager.requirePermissions.bind(permissionManager),
  requireRoles: permissionManager.requireRoles.bind(permissionManager),

  // 新增的增强功能
  getUserPermissionInfo: permissionManager.getUserPermissionInfo.bind(permissionManager),
  clearUserCache: permissionManager.clearUserCache.bind(permissionManager),
  getStats: permissionManager.getStats.bind(permissionManager),

  // 导出权限常量（保持兼容性）
  ROLES,
  PERMISSIONS,
  ROLE_PERMISSIONS
};
