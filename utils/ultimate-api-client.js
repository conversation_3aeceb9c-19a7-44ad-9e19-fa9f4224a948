/**
 * 终极API客户端 - 统一API调用接口
 * Ultimate API Client - Unified API Interface
 * 
 * 整合所有API调用逻辑，支持云函数和HTTP双模式
 * 提供业务模块化的API接口
 */

const request = require('./request.js');
const { API: API_CONST } = require('../constants/index.js');
const { cloudAPIAdapter } = require('./cloud-api-adapter.js');

/**
 * 终极API客户端类
 */
class UltimateAPIClient {
  constructor() {
    this.version = 'v2';
    this.requestQueue = new Map();
    this.cache = new Map();
    this.retryConfig = { maxRetries: 3, retryDelay: 1000 };
    this.init();
  }

  init() {
    // 初始化配置
    this.baseConfig = {
      timeout: 10000,
      retries: 3,
      cache: true,
      cacheExpiry: 60000
    };
  }

  /**
   * 智能API调用 - 优先云函数，回退HTTP
   */
  async smartApiCall(method, url, data = {}, options = {}) {
    try {
      const app = getApp();

      // 检查云开发是否可用
      if (app.globalData.isCloudEnabled) {
        try {
          console.log(`使用云函数调用: ${method} ${url}`);
          return await cloudAPIAdapter.request(method, url, data, { 
            fallbackToHttp: true,
            ...options 
          });
        } catch (cloudError) {
          console.warn('云函数调用失败，回退到HTTP请求:', cloudError);
        }
      }

      // 回退到传统HTTP请求
      console.log(`使用HTTP请求: ${method} ${url}`);
      return await this.httpApiCall(method, url, data);

    } catch (error) {
      console.error('API调用失败:', error);
      throw error;
    }
  }

  /**
   * HTTP API调用
   */
  async httpApiCall(method, url, data = {}) {
    const relativeUrl = this.toRelative(url);

    switch (method.toUpperCase()) {
      case 'GET':
        return request.get(relativeUrl, data);
      case 'POST':
        return request.post(relativeUrl, data);
      case 'PUT':
        return request.put(relativeUrl, data);
      case 'DELETE':
        return request.del(relativeUrl);
      default:
        throw new Error(`不支持的HTTP方法: ${method}`);
    }
  }

  /**
   * 转换为相对URL - 微信小程序兼容版本
   */
  toRelative(url) {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      // 微信小程序不支持URL构造函数，使用字符串处理
      const protocolIndex = url.indexOf('://');
      const hostStart = protocolIndex + 3;
      const pathStart = url.indexOf('/', hostStart);

      if (pathStart === -1) {
        return '/';
      }

      return url.substring(pathStart);
    }
    return url;
  }

  /**
   * GET请求
   */
  async get(url, params = {}, options = {}) {
    return this.smartApiCall('GET', url, params, options);
  }

  /**
   * POST请求
   */
  async post(url, data = {}, options = {}) {
    return this.smartApiCall('POST', url, data, options);
  }

  /**
   * PUT请求
   */
  async put(url, data = {}, options = {}) {
    return this.smartApiCall('PUT', url, data, options);
  }

  /**
   * DELETE请求
   */
  async delete(url, options = {}) {
    return this.smartApiCall('DELETE', url, {}, options);
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      cacheSize: this.cache.size,
      queueSize: this.requestQueue.size
    };
  }
}

// 创建单例实例
const ultimateAPIClient = new UltimateAPIClient();

/**
 * 业务API模块 - 按业务领域组织
 */
const businessAPI = {
  // 认证模块
  auth: {
    login: (data) => ultimateAPIClient.post(API_CONST.ENDPOINTS.AUTH.LOGIN, data),
    logout: () => ultimateAPIClient.post(API_CONST.ENDPOINTS.AUTH.LOGOUT),
    getUserInfo: () => ultimateAPIClient.get(API_CONST.ENDPOINTS.AUTH.USER_INFO),
    refreshToken: () => ultimateAPIClient.post(API_CONST.ENDPOINTS.AUTH.REFRESH),
    wechatLogin: (data) => ultimateAPIClient.post(API_CONST.ENDPOINTS.AUTH.WECHAT_LOGIN, data)
  },

  // 首页模块
  home: {
    getHomeData: () => ultimateAPIClient.get(API_CONST.ENDPOINTS.HOME.DATA),
    getAnnouncements: (params) => ultimateAPIClient.get(API_CONST.ENDPOINTS.HOME.ANNOUNCEMENTS, params)
  },

  // 健康监测模块
  health: {
    getRecords: (params) => ultimateAPIClient.get(API_CONST.ENDPOINTS.PRODUCTION.RECORDS, params),
    getRecordDetail: (id) => ultimateAPIClient.get(API_CONST.ENDPOINTS.PRODUCTION.RECORD_DETAIL(id)),
    createRecord: (data) => ultimateAPIClient.post(API_CONST.ENDPOINTS.PRODUCTION.CREATE_RECORD, data),
    updateRecord: (id, data) => ultimateAPIClient.put(API_CONST.ENDPOINTS.PRODUCTION.UPDATE_RECORD(id), data),
    deleteRecord: (id) => ultimateAPIClient.delete(API_CONST.ENDPOINTS.PRODUCTION.DELETE_RECORD(id)),
    aiDiagnosis: (data) => ultimateAPIClient.post(API_CONST.ENDPOINTS.PRODUCTION.AI_DIAGNOSIS, data)
  },

  // 物料管理模块
  materials: {
    getInventory: (params) => ultimateAPIClient.get(API_CONST.ENDPOINTS.PRODUCTION.AI_INVENTORY, params),
    saveInventoryRecord: (data) => ultimateAPIClient.post(API_CONST.ENDPOINTS.PRODUCTION.AI_INVENTORY, data),
    createPurchaseRequest: (data) => ultimateAPIClient.post(API_CONST.ENDPOINTS.PRODUCTION.PURCHASE_REQUESTS, data)
  },

  // 财务模块
  finance: {
    getFinanceData: (params) => ultimateAPIClient.get(API_CONST.ENDPOINTS.PRODUCTION.FINANCE, params),
    createReimbursementRequest: (data) => ultimateAPIClient.post(API_CONST.ENDPOINTS.PRODUCTION.REIMBURSEMENT_REQUESTS, data)
  },

  // 工具模块
  utils: {
    getKnowledgeList: (params) => ultimateAPIClient.get(API_CONST.ENDPOINTS.PRODUCTION.KNOWLEDGE, params),
    getKnowledgeDetail: (id) => ultimateAPIClient.get(API_CONST.ENDPOINTS.PRODUCTION.RECORD_DETAIL(id)),
    getReport: (params) => ultimateAPIClient.get(API_CONST.ENDPOINTS.PRODUCTION.REPORTS, params),
    getEnvironmentData: (params) => ultimateAPIClient.get(API_CONST.ENDPOINTS.PRODUCTION.ENVIRONMENT, params)
  }
};

// 导出接口
module.exports = {
  ultimateAPIClient,
  businessAPI,
  
  // 便捷方法导出
  get: (url, params, options) => ultimateAPIClient.get(url, params, options),
  post: (url, data, options) => ultimateAPIClient.post(url, data, options),
  put: (url, data, options) => ultimateAPIClient.put(url, data, options),
  delete: (url, options) => ultimateAPIClient.delete(url, options),
  
  // 兼容性导出
  API_ENDPOINTS: API_CONST.ENDPOINTS
};
