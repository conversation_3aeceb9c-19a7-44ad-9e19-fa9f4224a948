/**
 * 微信小程序云函数API适配器
 * WeChat Mini Program Cloud Function API Adapter
 * 
 * 功能特性：
 * 1. 统一云函数调用接口
 * 2. 自动错误处理和重试
 * 3. 请求/响应标准化
 * 4. HTTP请求回退机制
 * 5. 性能监控和日志记录
 */

const { apiResponseHandler } = require('./api-response-handler.js');

/**
 * 云函数映射表 - 将HTTP端点映射到云函数
 */
const CLOUD_FUNCTION_MAPPING = {
  // 认证相关
  'POST /api/v1/auth/login': 'login',
  'GET /api/v1/auth/user-info': 'getUserInfo',
  'POST /api/v1/auth/logout': 'getUserInfo',
  
  // 鹅群管理
  'GET /api/v1/flocks': { name: 'flockManagement', action: 'list' },
  'GET /api/v1/flocks/:id': { name: 'flockManagement', action: 'detail' },
  'POST /api/v1/flocks': { name: 'flockManagement', action: 'create' },
  'PUT /api/v1/flocks/:id': { name: 'flockManagement', action: 'update' },
  'DELETE /api/v1/flocks/:id': { name: 'flockManagement', action: 'delete' },
  
  // 健康管理
  'GET /api/v1/health/records': { name: 'healthManagement', action: 'list' },
  'POST /api/v1/health/records': { name: 'healthManagement', action: 'create' },
  'PUT /api/v1/health/records/:id': { name: 'healthManagement', action: 'update' },
  'DELETE /api/v1/health/records/:id': { name: 'healthManagement', action: 'delete' },
  'POST /api/v1/health/ai-diagnosis': { name: 'healthManagement', action: 'aiDiagnosis' },
  
  // 物料管理
  'GET /api/v1/materials': { name: 'materialManagement', action: 'list' },
  'POST /api/v1/materials': { name: 'materialManagement', action: 'create' },
  'PUT /api/v1/materials/:id': { name: 'materialManagement', action: 'update' },
  
  // 商城管理
  'GET /api/v1/shop/products': { name: 'shopManagement', action: 'getProducts' },
  'POST /api/v1/shop/orders': { name: 'shopManagement', action: 'createOrder' },
  'GET /api/v1/shop/orders': { name: 'shopManagement', action: 'getOrders' },
  
  // 平台管理（需要管理员权限）
  'GET /api/v1/admin/tenants': { name: 'tenantManagement', action: 'list' },
  'POST /api/v1/admin/tenants': { name: 'tenantManagement', action: 'create' },
  'GET /api/v1/admin/goose-prices': { name: 'goosePriceManagement', action: 'list' },
  'POST /api/v1/admin/goose-prices': { name: 'goosePriceManagement', action: 'create' },
  'GET /api/v1/admin/announcements': { name: 'announcementManagement', action: 'list' },
  'POST /api/v1/admin/announcements': { name: 'announcementManagement', action: 'create' },
  'GET /api/v1/admin/system': { name: 'systemManagement', action: 'getSystemInfo' },
  'POST /api/v1/admin/system': { name: 'systemManagement', action: 'updateSystemConfig' },

  // V2 API 映射
  'GET /api/v2/system/config': { name: 'systemManagement', action: 'getSystemConfig' },
  'POST /api/v2/system/config': { name: 'systemManagement', action: 'updateSystemConfig' },
  'GET /api/v2/home/<USER>': { name: 'homeManagement', action: 'getHomeData' },
  'GET /api/v2/home/<USER>': { name: 'homeManagement', action: 'getAnnouncements' },
  'GET /api/v2/auth/userinfo': { name: 'authManagement', action: 'getUserInfo' },
  'POST /api/v2/auth/login': { name: 'authManagement', action: 'login' },
  'POST /api/v2/auth/logout': { name: 'authManagement', action: 'logout' }
};

/**
 * 云API适配器类
 */
class CloudAPIAdapter {
  constructor() {
    this.requestId = 0;
    this.cache = new Map();
    this.retryConfig = {
      maxRetries: 3,
      retryDelay: 1000,
      retryDelayMultiplier: 2
    };
  }

  /**
   * 统一API请求入口
   * @param {string} method HTTP方法
   * @param {string} url 请求URL
   * @param {Object} data 请求数据
   * @param {Object} options 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async request(method, url, data = {}, options = {}) {
    const requestId = this.generateRequestId();
    const startTime = apiResponseHandler.performanceMonitor.startRequest(url);
    
    try {
      // 构建请求键值
      const requestKey = `${method.toUpperCase()} ${url}`;
      const cloudFunction = this.getCloudFunction(requestKey, url);
      
      if (!cloudFunction) {
        throw new Error(`未找到对应的云函数映射: ${requestKey}`);
      }
      
      // 检查缓存
      if (method.toUpperCase() === 'GET' && options.useCache) {
        const cachedResult = this.getFromCache(requestKey, data);
        if (cachedResult) {
          console.log(`使用缓存数据: ${requestKey}`);
          return cachedResult;
        }
      }
      
      // 调用云函数
      console.log(`调用云函数: ${cloudFunction.name || cloudFunction}`, { action: cloudFunction.action, data });
      
      const result = await this.callCloudFunction(cloudFunction, data, options);
      
      // 缓存GET请求结果
      if (method.toUpperCase() === 'GET' && options.useCache) {
        this.setCache(requestKey, data, result);
      }
      
      return result;
      
    } catch (error) {
      console.error(`云函数调用失败 [${requestId}]:`, error);
      
      // 如果允许回退到HTTP请求
      if (options.fallbackToHttp) {
        console.log(`回退到HTTP请求: ${method} ${url}`);
        throw error; // 抛出错误，让调用方处理HTTP回退
      }
      
      // 处理错误响应
      return apiResponseHandler.handleError(error, url);
    }
  }

  /**
   * 获取云函数映射
   * @param {string} requestKey 请求键
   * @param {string} url 原始URL
   * @returns {Object|string} 云函数配置
   */
  getCloudFunction(requestKey, url) {
    // 直接匹配
    if (CLOUD_FUNCTION_MAPPING[requestKey]) {
      return CLOUD_FUNCTION_MAPPING[requestKey];
    }
    
    // 参数化URL匹配
    for (const [pattern, config] of Object.entries(CLOUD_FUNCTION_MAPPING)) {
      if (this.matchUrlPattern(pattern, requestKey)) {
        return config;
      }
    }
    
    return null;
  }

  /**
   * URL模式匹配
   * @param {string} pattern 模式字符串
   * @param {string} requestKey 请求键
   * @returns {boolean} 是否匹配
   */
  matchUrlPattern(pattern, requestKey) {
    // 将 :id 等参数转换为正则表达式
    const regexPattern = pattern
      .replace(/:\w+/g, '[^/]+')  // :id -> [^/]+
      .replace(/\//g, '\\/');     // / -> \/
    
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(requestKey);
  }

  /**
   * 调用微信云函数
   * @param {Object|string} cloudFunction 云函数配置
   * @param {Object} data 请求数据
   * @param {Object} options 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async callCloudFunction(cloudFunction, data, options = {}) {
    const functionName = typeof cloudFunction === 'string' ? cloudFunction : cloudFunction.name;
    const action = typeof cloudFunction === 'object' ? cloudFunction.action : null;
    
    // 构建云函数参数
    const functionData = {
      ...data,
      ...(action && { action })
    };
    
    // 重试机制
    let lastError;
    for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        const result = await wx.cloud.callFunction({
          name: functionName,
          data: functionData
        });
        
        // 检查云函数执行结果
        if (result.result) {
          return await apiResponseHandler.handleResponse(result.result, functionName);
        } else {
          throw new Error('云函数返回数据格式错误');
        }
        
      } catch (error) {
        lastError = error;
        
        if (attempt < this.retryConfig.maxRetries) {
          const delay = this.retryConfig.retryDelay * Math.pow(this.retryConfig.retryDelayMultiplier, attempt);
          console.warn(`云函数调用失败，${delay}ms后重试 (${attempt + 1}/${this.retryConfig.maxRetries}):`, error.message);
          await this.sleep(delay);
        }
      }
    }
    
    throw lastError;
  }

  /**
   * 从缓存获取数据
   * @param {string} key 缓存键
   * @param {Object} params 请求参数
   * @returns {Object|null} 缓存数据
   */
  getFromCache(key, params) {
    const cacheKey = this.generateCacheKey(key, params);
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < 300000) { // 5分钟缓存
      return cached.data;
    }
    
    if (cached) {
      this.cache.delete(cacheKey);
    }
    
    return null;
  }

  /**
   * 设置缓存数据
   * @param {string} key 缓存键
   * @param {Object} params 请求参数
   * @param {Object} data 响应数据
   */
  setCache(key, params, data) {
    const cacheKey = this.generateCacheKey(key, params);
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
    
    // 清理过期缓存
    if (this.cache.size > 100) {
      this.cleanExpiredCache();
    }
  }

  /**
   * 生成缓存键
   * @param {string} key 基础键
   * @param {Object} params 参数
   * @returns {string} 缓存键
   */
  generateCacheKey(key, params) {
    const paramStr = JSON.stringify(params || {});
    return `${key}:${paramStr}`;
  }

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    const now = Date.now();
    const expiredKeys = [];
    
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > 300000) { // 5分钟过期
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => this.cache.delete(key));
    console.log(`清理过期缓存: ${expiredKeys.length} 项`);
  }

  /**
   * 生成请求ID
   * @returns {string} 请求ID
   */
  generateRequestId() {
    return `cloud_${++this.requestId}_${Date.now()}`;
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理所有缓存
   */
  clearCache() {
    this.cache.clear();
    console.log('已清理所有API缓存');
  }

  /**
   * 获取缓存状态
   * @returns {Object} 缓存统计信息
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// 创建全局实例
const cloudAPIAdapter = new CloudAPIAdapter();

// 导出适配器
module.exports = {
  CloudAPIAdapter,
  cloudAPIAdapter,
  CLOUD_FUNCTION_MAPPING
};