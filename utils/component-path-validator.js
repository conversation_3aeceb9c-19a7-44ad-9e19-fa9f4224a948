/**
 * 组件路径验证工具
 * 用于验证微信小程序组件路径是否正确
 */

class ComponentPathValidator {
  /**
   * 验证组件路径
   */
  static validateComponentPath(componentPath, basePath = '') {
    const fs = require('fs');
    const path = require('path');
    
    // 处理不同类型的路径
    let actualPath;
    
    if (componentPath.startsWith('/')) {
      // 绝对路径
      actualPath = path.join(process.cwd(), componentPath.substring(1));
    } else if (componentPath.startsWith('./') || componentPath.startsWith('../')) {
      // 相对路径
      actualPath = path.resolve(basePath, componentPath);
    } else if (componentPath.includes('://') || componentPath.includes('tdesign') || componentPath.includes('vant')) {
      // 第三方组件或内置组件
      return { valid: true, type: 'external' };
    } else {
      // 其他情况
      actualPath = path.resolve(basePath, componentPath);
    }
    
    // 检查组件文件
    const requiredFiles = ['.js', '.wxml', '.json'];
    const existingFiles = [];
    
    for (const ext of requiredFiles) {
      if (fs.existsSync(actualPath + ext)) {
        existingFiles.push(ext);
      }
    }
    
    return {
      valid: existingFiles.length === 3,
      actualPath,
      existingFiles,
      missingFiles: requiredFiles.filter(ext => !existingFiles.includes(ext))
    };
  }

  /**
   * 批量验证页面组件
   */
  static validatePageComponents(pageJsonPath) {
    const fs = require('fs');
    const path = require('path');
    
    try {
      const config = JSON.parse(fs.readFileSync(pageJsonPath, 'utf8'));
      const results = [];
      
      if (config.usingComponents) {
        const basePath = path.dirname(pageJsonPath);
        
        for (const [componentName, componentPath] of Object.entries(config.usingComponents)) {
          const result = this.validateComponentPath(componentPath, basePath);
          results.push({
            componentName,
            componentPath,
            ...result
          });
        }
      }
      
      return results;
    } catch (error) {
      return { error: error.message };
    }
  }
}

module.exports = ComponentPathValidator;