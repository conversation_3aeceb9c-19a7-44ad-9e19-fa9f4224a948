/**
 * 多租户数据分析云函数
 * Multi-Tenant Data Analytics Cloud Function
 * 
 * 基于Context7最佳实践的数据分析和报表生成
 * 支持平台级、租户级、用户级多维度数据分析
 */

const { apiHandler } = require('../../../utils/api-handler');
const { APIError, ERROR_CODES } = require('../../../utils/api-errors');
const { hasPermission, PERMISSIONS } = require('../../../utils/role-permission');

/**
 * 获取平台数据分析总览
 */
async function getPlatformAnalytics(user, secureQuery, logDataAccess) {
  // 验证权限
  if (!hasPermission(user.role, PERMISSIONS.PLATFORM_ANALYTICS_VIEW)) {
    throw new APIError('权限不足', ERROR_CODES.PERMISSION_DENIED);
  }

  // 记录数据访问日志
  await logDataAccess({
    userId: user._id,
    tenantId: user.tenant_id,
    action: 'GET_PLATFORM_ANALYTICS',
    dataType: 'PLATFORM_ANALYTICS',
    timestamp: new Date()
  });

  // 获取平台总体统计
  const totalStats = await secureQuery('admin_analytics', {
    type: 'platform_overview'
  }, { limit: 1, sort: { timestamp: -1 } });

  // 获取最近30天的趋势数据
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const trendData = await secureQuery('admin_analytics', {
    type: 'daily_platform_stats',
    timestamp: { $gte: thirtyDaysAgo }
  }, { sort: { timestamp: 1 } });

  // 获取租户分析数据
  const tenantAnalytics = await secureQuery('admin_analytics', {
    type: 'tenant_summary'
  }, { limit: 10, sort: { active_users: -1 } });

  // 获取收入分析
  const revenueAnalytics = await secureQuery('admin_analytics', {
    type: 'revenue_analysis'
  }, { limit: 1, sort: { timestamp: -1 } });

  return {
    overview: totalStats.data[0] || getDefaultPlatformStats(),
    trends: trendData.data || [],
    topTenants: tenantAnalytics.data || [],
    revenue: revenueAnalytics.data[0] || getDefaultRevenueStats(),
    lastUpdated: new Date().toISOString()
  };
}

/**
 * 获取租户数据分析
 */
async function getTenantAnalytics(tenantId, user, secureQuery, logDataAccess) {
  // 验证权限 - 只有平台管理员或对应租户管理员可查看
  if (!hasPermission(user.role, PERMISSIONS.PLATFORM_ANALYTICS_VIEW) &&
      !(hasPermission(user.role, PERMISSIONS.TENANT_ANALYTICS_VIEW) && user.tenant_id === tenantId)) {
    throw new APIError('权限不足', ERROR_CODES.PERMISSION_DENIED);
  }

  // 记录数据访问
  await logDataAccess({
    userId: user._id,
    tenantId: user.tenant_id,
    action: 'GET_TENANT_ANALYTICS',
    targetTenantId: tenantId,
    dataType: 'TENANT_ANALYTICS',
    timestamp: new Date()
  });

  // 获取租户基础信息
  const tenantInfo = await secureQuery('tenants', {
    _id: tenantId
  }, { limit: 1 });

  if (tenantInfo.data.length === 0) {
    throw new APIError('租户不存在', ERROR_CODES.TENANT_NOT_FOUND);
  }

  // 获取用户统计
  const userStats = await secureQuery('analytics_user_stats', {
    tenant_id: tenantId
  }, { limit: 1, sort: { timestamp: -1 } });

  // 获取鹅群统计
  const flockStats = await secureQuery('analytics_flock_stats', {
    tenant_id: tenantId
  }, { limit: 1, sort: { timestamp: -1 } });

  // 获取收入统计
  const incomeStats = await secureQuery('analytics_income_stats', {
    tenant_id: tenantId
  }, { limit: 1, sort: { timestamp: -1 } });

  // 获取最近30天趋势
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const trends = await secureQuery('analytics_daily_stats', {
    tenant_id: tenantId,
    date: { $gte: thirtyDaysAgo }
  }, { sort: { date: 1 } });

  return {
    tenant: tenantInfo.data[0],
    userStats: userStats.data[0] || getDefaultUserStats(),
    flockStats: flockStats.data[0] || getDefaultFlockStats(),
    incomeStats: incomeStats.data[0] || getDefaultIncomeStats(),
    trends: trends.data || [],
    lastUpdated: new Date().toISOString()
  };
}

/**
 * 生成数据报表
 */
async function generateReport(params, user, secureQuery, logDataAccess) {
  const { reportType, startDate, endDate, tenantIds, format = 'json' } = params;

  // 验证权限
  if (!hasPermission(user.role, PERMISSIONS.PLATFORM_DATA_EXPORT)) {
    throw new APIError('权限不足', ERROR_CODES.PERMISSION_DENIED);
  }

  // 记录操作
  await logDataAccess({
    userId: user._id,
    tenantId: user.tenant_id,
    action: 'GENERATE_REPORT',
    dataType: 'ANALYTICS_REPORT',
    params: { reportType, startDate, endDate, tenantIds, format },
    timestamp: new Date()
  });

  const start = new Date(startDate);
  const end = new Date(endDate);

  let reportData = {};

  switch (reportType) {
    case 'platform_summary':
      reportData = await generatePlatformSummaryReport(start, end, secureQuery);
      break;
    
    case 'tenant_performance':
      reportData = await generateTenantPerformanceReport(start, end, tenantIds, secureQuery);
      break;
    
    case 'user_activity':
      reportData = await generateUserActivityReport(start, end, tenantIds, secureQuery);
      break;
    
    case 'revenue_analysis':
      reportData = await generateRevenueAnalysisReport(start, end, tenantIds, secureQuery);
      break;
    
    default:
      throw new APIError('不支持的报表类型', ERROR_CODES.INVALID_PARAMS);
  }

  // 格式化输出
  if (format === 'csv') {
    reportData.csvData = convertToCSV(reportData.data);
  }

  return {
    reportType,
    generatedAt: new Date().toISOString(),
    dateRange: { startDate, endDate },
    format,
    ...reportData
  };
}

/**
 * 获取图表数据
 */
async function getChartData(params, user, secureQuery, logDataAccess) {
  const { chartType, timeRange, tenantId, metrics } = params;

  // 验证权限
  const requiredPermission = tenantId ? 
    PERMISSIONS.TENANT_ANALYTICS_VIEW : 
    PERMISSIONS.PLATFORM_ANALYTICS_VIEW;
    
  if (!hasPermission(user.role, requiredPermission)) {
    throw new APIError('权限不足', ERROR_CODES.PERMISSION_DENIED);
  }

  // 记录访问
  await logDataAccess({
    userId: user._id,
    tenantId: user.tenant_id,
    action: 'GET_CHART_DATA',
    dataType: 'CHART_DATA',
    params: { chartType, timeRange, tenantId },
    timestamp: new Date()
  });

  const endDate = new Date();
  const startDate = new Date();
  
  // 根据时间范围计算开始日期
  switch (timeRange) {
    case '7d':
      startDate.setDate(endDate.getDate() - 7);
      break;
    case '30d':
      startDate.setDate(endDate.getDate() - 30);
      break;
    case '90d':
      startDate.setDate(endDate.getDate() - 90);
      break;
    default:
      startDate.setDate(endDate.getDate() - 30);
  }

  const query = {
    date: { $gte: startDate, $lte: endDate }
  };

  if (tenantId) {
    query.tenant_id = tenantId;
  }

  let chartData = {};

  switch (chartType) {
    case 'user_growth':
      chartData = await getUserGrowthData(query, secureQuery);
      break;
    
    case 'revenue_trend':
      chartData = await getRevenueTrendData(query, secureQuery);
      break;
    
    case 'flock_activity':
      chartData = await getFlockActivityData(query, secureQuery);
      break;
    
    case 'tenant_comparison':
      chartData = await getTenantComparisonData(query, secureQuery);
      break;
    
    default:
      throw new APIError('不支持的图表类型', ERROR_CODES.INVALID_PARAMS);
  }

  return {
    chartType,
    timeRange,
    tenantId,
    data: chartData,
    lastUpdated: new Date().toISOString()
  };
}

// ========== 辅助方法 ==========

function getDefaultPlatformStats() {
  return {
    totalUsers: 0,
    totalTenants: 0,
    activeFlocks: 0,
    monthlyRevenue: 0,
    systemHealth: 'good'
  };
}

function getDefaultRevenueStats() {
  return {
    currentMonth: 0,
    lastMonth: 0,
    growth: 0,
    totalRevenue: 0
  };
}

function getDefaultUserStats() {
  return {
    totalUsers: 0,
    activeUsers: 0,
    newUsersToday: 0,
    userRetentionRate: 0
  };
}

function getDefaultFlockStats() {
  return {
    totalFlocks: 0,
    activeFlocks: 0,
    newFlocksToday: 0,
    avgFlockSize: 0
  };
}

function getDefaultIncomeStats() {
  return {
    monthlyIncome: 0,
    incomeGrowth: 0,
    avgIncomePerUser: 0,
    totalIncome: 0
  };
}

async function generatePlatformSummaryReport(startDate, endDate, secureQuery) {
  // 获取平台汇总数据
  const summaryData = await secureQuery('analytics_platform_summary', {
    date: { $gte: startDate, $lte: endDate }
  }, { sort: { date: 1 } });

  return {
    title: '平台数据汇总报表',
    data: summaryData.data,
    summary: {
      totalRecords: summaryData.data.length,
      dateRange: `${startDate.toISOString().split('T')[0]} 至 ${endDate.toISOString().split('T')[0]}`
    }
  };
}

async function generateTenantPerformanceReport(startDate, endDate, tenantIds, secureQuery) {
  const query = {
    date: { $gte: startDate, $lte: endDate }
  };
  
  if (tenantIds && tenantIds.length > 0) {
    query.tenant_id = { $in: tenantIds };
  }

  const performanceData = await secureQuery('analytics_tenant_performance', query, { 
    sort: { date: 1, tenant_id: 1 } 
  });

  return {
    title: '租户绩效分析报表',
    data: performanceData.data,
    summary: {
      totalTenants: new Set(performanceData.data.map(d => d.tenant_id)).size,
      totalRecords: performanceData.data.length
    }
  };
}

async function generateUserActivityReport(startDate, endDate, tenantIds, secureQuery) {
  const query = {
    date: { $gte: startDate, $lte: endDate }
  };
  
  if (tenantIds && tenantIds.length > 0) {
    query.tenant_id = { $in: tenantIds };
  }

  const activityData = await secureQuery('analytics_user_activity', query, {
    sort: { date: 1 }
  });

  return {
    title: '用户活动分析报表',
    data: activityData.data,
    summary: {
      totalRecords: activityData.data.length,
      avgDailyActiveUsers: activityData.data.reduce((sum, d) => sum + d.activeUsers, 0) / activityData.data.length
    }
  };
}

async function generateRevenueAnalysisReport(startDate, endDate, tenantIds, secureQuery) {
  const query = {
    date: { $gte: startDate, $lte: endDate }
  };
  
  if (tenantIds && tenantIds.length > 0) {
    query.tenant_id = { $in: tenantIds };
  }

  const revenueData = await secureQuery('analytics_revenue', query, {
    sort: { date: 1 }
  });

  const totalRevenue = revenueData.data.reduce((sum, d) => sum + d.revenue, 0);

  return {
    title: '收入分析报表',
    data: revenueData.data,
    summary: {
      totalRevenue,
      avgDailyRevenue: totalRevenue / revenueData.data.length,
      totalRecords: revenueData.data.length
    }
  };
}

async function getUserGrowthData(query, secureQuery) {
  const data = await secureQuery('analytics_user_growth', query, {
    sort: { date: 1 }
  });

  return {
    labels: data.data.map(d => d.date),
    datasets: [{
      label: '新增用户',
      data: data.data.map(d => d.newUsers)
    }, {
      label: '活跃用户',
      data: data.data.map(d => d.activeUsers)
    }]
  };
}

async function getRevenueTrendData(query, secureQuery) {
  const data = await secureQuery('analytics_revenue_trend', query, {
    sort: { date: 1 }
  });

  return {
    labels: data.data.map(d => d.date),
    datasets: [{
      label: '每日收入',
      data: data.data.map(d => d.dailyRevenue)
    }, {
      label: '累计收入',
      data: data.data.map(d => d.cumulativeRevenue)
    }]
  };
}

async function getFlockActivityData(query, secureQuery) {
  const data = await secureQuery('analytics_flock_activity', query, {
    sort: { date: 1 }
  });

  return {
    labels: data.data.map(d => d.date),
    datasets: [{
      label: '新建鹅群',
      data: data.data.map(d => d.newFlocks)
    }, {
      label: '活跃鹅群',
      data: data.data.map(d => d.activeFlocks)
    }]
  };
}

async function getTenantComparisonData(query, secureQuery) {
  const data = await secureQuery('analytics_tenant_comparison', query);

  return {
    labels: data.data.map(d => d.tenantName),
    datasets: [{
      label: '用户数量',
      data: data.data.map(d => d.userCount)
    }, {
      label: '月收入',
      data: data.data.map(d => d.monthlyRevenue)
    }]
  };
}

function convertToCSV(data) {
  if (!data || data.length === 0) return '';
  
  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row => headers.map(header => row[header] || '').join(','))
  ].join('\n');
  
  return csvContent;
}

// ========== 云函数入口 ==========

exports.main = apiHandler(async (params, context) => {
  const { action, data } = params;
  const { user, secureQuery, logDataAccess } = context;

  switch (action) {
    case 'getPlatformAnalytics':
      return await getPlatformAnalytics(user, secureQuery, logDataAccess);
    
    case 'getTenantAnalytics':
      return await getTenantAnalytics(data.tenantId, user, secureQuery, logDataAccess);
    
    case 'generateReport':
      return await generateReport(data, user, secureQuery, logDataAccess);
    
    case 'getChartData':
      return await getChartData(data, user, secureQuery, logDataAccess);
    
    default:
      throw new APIError('不支持的操作', ERROR_CODES.UNSUPPORTED_ACTION);
  }
});