/**
 * 租户管理云函数
 * Tenant Management Cloud Function
 * 
 * 基于Context7最佳实践的完整租户生命周期管理
 * 使用API标准化处理器，确保数据隔离和权限安全
 * 
 * 功能：
 * - 租户注册、配置、权限分配
 * - 订阅管理和计费监控
 * - 租户使用统计和分析
 * - 租户数据隔离管理
 * - 操作审计日志
 */

const cloud = require('wx-server-sdk');
const { apiHandler, ValidationHelper, APIError, ERROR_CODES } = require('../../../utils/api-standard');

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

/**
 * 云函数入口函数 - 使用API标准化处理器
 */
exports.main = apiHandler(async (params, context) => {
  const { action, data } = params;
  const { user, secureQuery, logDataAccess } = context;

  // 权限验证：只有平台管理员可以操作
  if (!['super_admin', 'platform_admin'].includes(user.role)) {
    throw new APIError('平台租户管理权限不足', ERROR_CODES.PERMISSION_DENIED);
  }

  try {
    switch (action) {
      case 'createTenant':
        return await createTenant(data, user);
      case 'getTenantList':
        return await getTenantList(data);
      case 'getTenantDetail':
        return await getTenantDetail(data);
      case 'updateTenant':
        return await updateTenant(data, user);
      case 'updateTenantSubscription':
        return await updateTenantSubscription(data, user);
      case 'getTenantStats':
        return await getTenantStats(data);
      case 'getTenantUsage':
        return await getTenantUsage(data);
      case 'suspendTenant':
        return await suspendTenant(data, user);
      case 'activateTenant':
        return await activateTenant(data, user);
      default:
        return {
          success: false,
          error: '不支持的操作类型'
        };
    }
  } catch (error) {
    console.error('租户管理云函数执行失败:', error);
    return {
      success: false,
      error: error.message || '服务器内部错误'
    };
  }
});

/**
 * 创建租户
 */
async function createTenant(data, userInfo) {
  const { 
    tenant_code,
    company_name,
    contact_name,
    contact_phone,
    contact_email,
    subscription_plan = 'trial'
  } = data;
  
  // 数据验证
  if (!tenant_code || !company_name || !contact_name) {
    return {
      success: false,
      error: '缺少必要参数：租户代码、公司名称、联系人'
    };
  }

  // 检查租户代码是否已存在
  const existingTenant = await db.collection('tenants').where({
    tenant_code: tenant_code
  }).get();

  if (existingTenant.data.length > 0) {
    return {
      success: false,
      error: '租户代码已存在'
    };
  }

  // 获取订阅计划配置
  const planConfig = getSubscriptionPlanConfig(subscription_plan);
  
  const tenantData = {
    tenant_code,
    company_name,
    contact_name,
    contact_phone,
    contact_email,
    subscription_plan,
    subscription_start: new Date(),
    subscription_end: new Date(Date.now() + planConfig.duration * 24 * 60 * 60 * 1000),
    features: planConfig.features,
    status: 'active',
    settings: {
      max_users: planConfig.max_users,
      max_flocks: planConfig.max_flocks,
      storage_limit: planConfig.storage_limit
    },
    usage_stats: {
      user_count: 0,
      flock_count: 0,
      storage_used: 0,
      api_calls: 0
    },
    created_by: userInfo._id,
    created_at: new Date(),
    updated_at: new Date()
  };

  try {
    const result = await db.collection('tenants').add({
      data: tenantData
    });

    // 创建租户管理员用户
    await createTenantAdmin(result._id, data, userInfo);

    // 记录操作日志
    await logPlatformOperation({
      operator_id: userInfo._id,
      operator_name: userInfo.nickname,
      action: 'create_tenant',
      target: 'tenants',
      target_id: result._id,
      details: `创建租户：${company_name} (${tenant_code})`
    });

    return {
      success: true,
      data: {
        id: result._id,
        ...tenantData
      }
    };
  } catch (error) {
    console.error('创建租户失败:', error);
    return {
      success: false,
      error: '创建租户失败'
    };
  }
}

/**
 * 获取租户列表
 */
async function getTenantList(data) {
  const { 
    page = 1, 
    limit = 20, 
    status, 
    subscription_plan,
    sortBy = 'created_at' 
  } = data;
  
  let whereCondition = {};

  // 添加筛选条件
  if (status) {
    whereCondition.status = status;
  }
  
  if (subscription_plan) {
    whereCondition.subscription_plan = subscription_plan;
  }

  try {
    const result = await db.collection('tenants')
      .where(whereCondition)
      .orderBy(sortBy, 'desc')
      .skip((page - 1) * limit)
      .limit(limit)
      .get();

    // 获取总数
    const countResult = await db.collection('tenants')
      .where(whereCondition)
      .count();

    return {
      success: true,
      data: {
        list: result.data,
        total: countResult.total,
        page,
        limit,
        totalPages: Math.ceil(countResult.total / limit)
      }
    };
  } catch (error) {
    console.error('获取租户列表失败:', error);
    return {
      success: false,
      error: '获取租户列表失败'
    };
  }
}

/**
 * 获取租户详情
 */
async function getTenantDetail(data) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      error: '缺少租户ID'
    };
  }

  try {
    const result = await db.collection('tenants').doc(id).get();
    
    if (!result.data) {
      return {
        success: false,
        error: '租户不存在'
      };
    }

    // 获取租户用户数量
    const userCount = await db.collection('users').where({
      tenant_id: id
    }).count();

    // 获取租户鹅群数量
    const flockCount = await db.collection('flocks').where({
      tenant_id: id
    }).count();

    const tenantDetail = {
      ...result.data,
      usage_stats: {
        ...result.data.usage_stats,
        user_count: userCount.total,
        flock_count: flockCount.total
      }
    };

    return {
      success: true,
      data: tenantDetail
    };
  } catch (error) {
    console.error('获取租户详情失败:', error);
    return {
      success: false,
      error: '获取租户详情失败'
    };
  }
}

/**
 * 更新租户信息
 */
async function updateTenant(data, userInfo) {
  const { id, ...updateData } = data;
  
  if (!id) {
    return {
      success: false,
      error: '缺少租户ID'
    };
  }

  try {
    const result = await db.collection('tenants').doc(id).update({
      data: {
        ...updateData,
        updated_at: new Date(),
        updated_by: userInfo._id
      }
    });

    // 记录操作日志
    await logPlatformOperation({
      operator_id: userInfo._id,
      operator_name: userInfo.nickname,
      action: 'update_tenant',
      target: 'tenants',
      target_id: id,
      details: `更新租户信息`
    });

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('更新租户失败:', error);
    return {
      success: false,
      error: '更新租户失败'
    };
  }
}

/**
 * 更新租户订阅
 */
async function updateTenantSubscription(data, userInfo) {
  const { id, subscription_plan, duration_months = 12 } = data;
  
  if (!id || !subscription_plan) {
    return {
      success: false,
      error: '缺少必要参数：租户ID、订阅计划'
    };
  }

  const planConfig = getSubscriptionPlanConfig(subscription_plan);
  
  try {
    const updateData = {
      subscription_plan,
      subscription_start: new Date(),
      subscription_end: new Date(Date.now() + duration_months * 30 * 24 * 60 * 60 * 1000),
      features: planConfig.features,
      settings: {
        max_users: planConfig.max_users,
        max_flocks: planConfig.max_flocks,
        storage_limit: planConfig.storage_limit
      },
      updated_at: new Date(),
      updated_by: userInfo._id
    };

    const result = await db.collection('tenants').doc(id).update({
      data: updateData
    });

    // 记录操作日志
    await logPlatformOperation({
      operator_id: userInfo._id,
      operator_name: userInfo.nickname,
      action: 'update_tenant_subscription',
      target: 'tenants',
      target_id: id,
      details: `更新租户订阅：${subscription_plan}`
    });

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('更新租户订阅失败:', error);
    return {
      success: false,
      error: '更新租户订阅失败'
    };
  }
}

/**
 * 获取租户统计信息
 */
async function getTenantStats(data) {
  try {
    // 获取各状态租户数量
    const statusStats = await db.collection('tenants')
      .aggregate()
      .group({
        _id: '$status',
        count: _.sum(1)
      })
      .end();

    // 获取各订阅计划租户数量
    const planStats = await db.collection('tenants')
      .aggregate()
      .group({
        _id: '$subscription_plan',
        count: _.sum(1)
      })
      .end();

    // 获取总用户数
    const totalUsers = await db.collection('users').count();

    // 获取总鹅群数
    const totalFlocks = await db.collection('flocks').count();

    return {
      success: true,
      data: {
        statusStats: statusStats.list,
        planStats: planStats.list,
        totalUsers: totalUsers.total,
        totalFlocks: totalFlocks.total
      }
    };
  } catch (error) {
    console.error('获取租户统计失败:', error);
    return {
      success: false,
      error: '获取租户统计失败'
    };
  }
}

/**
 * 获取租户使用情况
 */
async function getTenantUsage(data) {
  const { id, days = 30 } = data;
  
  if (!id) {
    return {
      success: false,
      error: '缺少租户ID'
    };
  }

  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  try {
    // 这里可以实现更复杂的使用统计逻辑
    // 例如：API调用次数、存储使用量、活跃用户数等
    
    const tenant = await db.collection('tenants').doc(id).get();
    
    return {
      success: true,
      data: {
        tenant_info: tenant.data,
        usage_period: {
          start_date: startDate,
          end_date: new Date(),
          days: days
        },
        // 这里可以添加更多使用统计数据
        usage_details: tenant.data.usage_stats || {}
      }
    };
  } catch (error) {
    console.error('获取租户使用情况失败:', error);
    return {
      success: false,
      error: '获取租户使用情况失败'
    };
  }
}

/**
 * 暂停租户
 */
async function suspendTenant(data, userInfo) {
  const { id, reason } = data;
  
  if (!id) {
    return {
      success: false,
      error: '缺少租户ID'
    };
  }

  try {
    const result = await db.collection('tenants').doc(id).update({
      data: {
        status: 'suspended',
        suspend_reason: reason || '管理员操作',
        suspended_at: new Date(),
        suspended_by: userInfo._id,
        updated_at: new Date()
      }
    });

    // 记录操作日志
    await logPlatformOperation({
      operator_id: userInfo._id,
      operator_name: userInfo.nickname,
      action: 'suspend_tenant',
      target: 'tenants',
      target_id: id,
      details: `暂停租户：${reason || '管理员操作'}`
    });

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('暂停租户失败:', error);
    return {
      success: false,
      error: '暂停租户失败'
    };
  }
}

/**
 * 激活租户
 */
async function activateTenant(data, userInfo) {
  const { id } = data;
  
  if (!id) {
    return {
      success: false,
      error: '缺少租户ID'
    };
  }

  try {
    const result = await db.collection('tenants').doc(id).update({
      data: {
        status: 'active',
        activated_at: new Date(),
        activated_by: userInfo._id,
        updated_at: new Date()
      }
    });

    // 记录操作日志
    await logPlatformOperation({
      operator_id: userInfo._id,
      operator_name: userInfo.nickname,
      action: 'activate_tenant',
      target: 'tenants',
      target_id: id,
      details: `激活租户`
    });

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('激活租户失败:', error);
    return {
      success: false,
      error: '激活租户失败'
    };
  }
}

/**
 * 创建租户管理员用户
 */
async function createTenantAdmin(tenantId, tenantData, creator) {
  try {
    const adminData = {
      tenant_id: tenantId,
      nickname: tenantData.contact_name,
      phone: tenantData.contact_phone,
      email: tenantData.contact_email,
      role: 'tenant_owner',
      status: 'active',
      permissions: ['tenant_admin'],
      created_by: creator._id,
      created_at: new Date(),
      updated_at: new Date()
    };

    await db.collection('users').add({
      data: adminData
    });
  } catch (error) {
    console.error('创建租户管理员失败:', error);
  }
}

/**
 * 获取订阅计划配置
 */
function getSubscriptionPlanConfig(plan) {
  const configs = {
    trial: {
      duration: 30, // 天
      max_users: 5,
      max_flocks: 10,
      storage_limit: 100, // MB
      features: ['basic_management']
    },
    basic: {
      duration: 365,
      max_users: 20,
      max_flocks: 50,
      storage_limit: 500,
      features: ['basic_management', 'data_export']
    },
    standard: {
      duration: 365,
      max_users: 100,
      max_flocks: 200,
      storage_limit: 2000,
      features: ['basic_management', 'data_export', 'health_management', 'production_management']
    },
    premium: {
      duration: 365,
      max_users: 500,
      max_flocks: 1000,
      storage_limit: 10000,
      features: ['basic_management', 'data_export', 'health_management', 'production_management', 'shop_module', 'ai_diagnosis']
    },
    enterprise: {
      duration: 365,
      max_users: -1, // 无限制
      max_flocks: -1,
      storage_limit: -1,
      features: ['all_features']
    }
  };

  return configs[plan] || configs.trial;
}

/**
 * 获取用户信息
 */
async function getUserInfo(openid) {
  try {
    const result = await db.collection('users').where({
      openid: openid
    }).get();
    
    return result.data[0] || null;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
}

/**
 * 权限检查
 */
function hasPermission(userRole, permission) {
  return ['super_admin', 'platform_admin'].includes(userRole);
}

/**
 * 记录平台操作日志
 */
async function logPlatformOperation(logData) {
  try {
    await db.collection('platform_operation_logs').add({
      data: {
        ...logData,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('记录操作日志失败:', error);
  }
}
