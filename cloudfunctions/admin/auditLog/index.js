/**
 * 权限控制和操作日志记录云函数
 * Permission Control and Operation Logging Cloud Function
 * 
 * 基于Context7最佳实践的完整权限管理和审计日志系统
 * 支持角色权限验证、操作记录、安全审计等功能
 */

const { apiHandler } = require('../../../utils/api-handler');
const { APIError, ERROR_CODES } = require('../../../utils/api-errors');
const { hasPermission, PERMISSIONS, getAllPermissions } = require('../../../utils/role-permission');

/**
 * 记录用户操作日志
 */
async function logUserOperation(operation, user, secureQuery, logDataAccess) {
  const {
    action,
    resourceType,
    resourceId,
    details,
    clientInfo,
    result = 'success'
  } = operation;

  try {
    // 创建操作日志记录
    const logEntry = {
      user_id: user._id,
      tenant_id: user.tenant_id,
      user_role: user.role,
      user_name: user.nickname || user.openid,
      
      // 操作信息
      action,
      resource_type: resourceType,
      resource_id: resourceId,
      operation_details: details,
      operation_result: result,
      
      // 客户端信息
      client_info: {
        ip_address: clientInfo?.ip || 'unknown',
        user_agent: clientInfo?.userAgent || 'unknown',
        device_type: clientInfo?.deviceType || 'unknown',
        app_version: clientInfo?.appVersion || 'unknown'
      },
      
      // 时间信息
      timestamp: new Date(),
      date_string: new Date().toISOString().split('T')[0],
      hour: new Date().getHours()
    };

    // 保存操作日志
    await secureQuery.insert('operation_logs', logEntry);

    // 如果是敏感操作，额外记录到安全审计日志
    if (isSensitiveOperation(action, resourceType)) {
      await recordSecurityAuditLog(logEntry, secureQuery);
    }

    // 检查是否需要触发安全告警
    await checkForSecurityAlerts(logEntry, user, secureQuery);

    return {
      logged: true,
      logId: logEntry._id || 'generated_id',
      timestamp: logEntry.timestamp
    };

  } catch (error) {
    console.error('记录操作日志失败:', error);
    // 日志记录失败不应该影响主要操作
    return {
      logged: false,
      error: error.message
    };
  }
}

/**
 * 验证用户权限
 */
async function verifyUserPermission(params, user, secureQuery, logDataAccess) {
  const { requiredPermissions, resourceType, resourceId, action } = params;

  // 记录权限检查请求
  await logDataAccess({
    userId: user._id,
    tenantId: user.tenant_id,
    action: 'PERMISSION_CHECK',
    dataType: 'PERMISSION_VERIFICATION',
    params: { requiredPermissions, resourceType, resourceId, action },
    timestamp: new Date()
  });

  // 获取用户当前角色和权限
  const userPermissions = await getUserPermissions(user, secureQuery);

  const verificationResult = {
    user_id: user._id,
    user_role: user.role,
    required_permissions: requiredPermissions,
    verification_timestamp: new Date(),
    permissions_granted: [],
    permissions_denied: [],
    overall_access: false
  };

  // 验证每个必需权限
  for (const permission of requiredPermissions) {
    const hasAccess = hasPermission(user.role, permission) && 
                     userPermissions.permissions.includes(permission);
    
    if (hasAccess) {
      verificationResult.permissions_granted.push(permission);
    } else {
      verificationResult.permissions_denied.push(permission);
    }
  }

  // 判断整体访问权限
  verificationResult.overall_access = verificationResult.permissions_denied.length === 0;

  // 如果是资源级权限检查，进行额外验证
  if (resourceType && resourceId) {
    const resourceAccess = await checkResourceAccess(
      user, resourceType, resourceId, verificationResult.overall_access, secureQuery
    );
    verificationResult.resource_access = resourceAccess;
    verificationResult.overall_access = verificationResult.overall_access && resourceAccess.granted;
  }

  // 记录权限验证日志
  await logUserOperation({
    action: 'PERMISSION_VERIFICATION',
    resourceType: 'PERMISSION_SYSTEM',
    resourceId: user._id,
    details: verificationResult,
    result: verificationResult.overall_access ? 'granted' : 'denied'
  }, user, secureQuery, logDataAccess);

  return verificationResult;
}

/**
 * 获取操作日志
 */
async function getOperationLogs(params, user, secureQuery, logDataAccess) {
  // 验证权限
  if (!hasPermission(user.role, PERMISSIONS.PLATFORM_AUDIT_LOG_VIEW)) {
    throw new APIError('权限不足', ERROR_CODES.PERMISSION_DENIED);
  }

  const {
    startDate,
    endDate,
    userId,
    tenantId,
    action,
    resourceType,
    result,
    page = 1,
    limit = 50
  } = params;

  // 记录日志查询访问
  await logDataAccess({
    userId: user._id,
    tenantId: user.tenant_id,
    action: 'VIEW_OPERATION_LOGS',
    dataType: 'OPERATION_LOGS',
    params,
    timestamp: new Date()
  });

  // 构建查询条件
  const query = {};
  
  if (startDate || endDate) {
    query.timestamp = {};
    if (startDate) query.timestamp.$gte = new Date(startDate);
    if (endDate) query.timestamp.$lte = new Date(endDate);
  }
  
  if (userId) query.user_id = userId;
  if (tenantId) query.tenant_id = tenantId;
  if (action) query.action = { $regex: action, $options: 'i' };
  if (resourceType) query.resource_type = resourceType;
  if (result) query.operation_result = result;

  // 非平台管理员只能查看自己租户的日志
  if (!hasPermission(user.role, PERMISSIONS.PLATFORM_ADMIN)) {
    query.tenant_id = user.tenant_id;
  }

  // 查询日志
  const logs = await secureQuery('operation_logs', query, {
    sort: { timestamp: -1 },
    limit,
    skip: (page - 1) * limit
  });

  // 获取总数
  const total = await secureQuery.count('operation_logs', query);

  return {
    logs: logs.data,
    pagination: {
      page,
      limit,
      total: total.count,
      totalPages: Math.ceil(total.count / limit)
    },
    query: query,
    retrievedAt: new Date().toISOString()
  };
}

/**
 * 获取安全审计报告
 */
async function getSecurityAuditReport(params, user, secureQuery, logDataAccess) {
  // 验证权限
  if (!hasPermission(user.role, PERMISSIONS.PLATFORM_SECURITY_AUDIT)) {
    throw new APIError('权限不足', ERROR_CODES.PERMISSION_DENIED);
  }

  const { startDate, endDate, reportType = 'summary' } = params;

  // 记录审计报告访问
  await logDataAccess({
    userId: user._id,
    tenantId: user.tenant_id,
    action: 'GENERATE_SECURITY_AUDIT_REPORT',
    dataType: 'SECURITY_AUDIT_REPORT',
    params,
    timestamp: new Date()
  });

  const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  const end = endDate ? new Date(endDate) : new Date();

  const auditReport = {
    reportType,
    dateRange: {
      startDate: start.toISOString(),
      endDate: end.toISOString()
    },
    generatedAt: new Date().toISOString(),
    generatedBy: {
      userId: user._id,
      userName: user.nickname || user.openid,
      role: user.role
    }
  };

  switch (reportType) {
    case 'summary':
      auditReport.data = await generateSecuritySummaryReport(start, end, secureQuery);
      break;
    
    case 'detailed':
      auditReport.data = await generateDetailedSecurityReport(start, end, secureQuery);
      break;
    
    case 'anomalies':
      auditReport.data = await generateAnomalyDetectionReport(start, end, secureQuery);
      break;
    
    default:
      throw new APIError('不支持的报告类型', ERROR_CODES.INVALID_PARAMS);
  }

  return auditReport;
}

/**
 * 获取用户权限详情
 */
async function getUserPermissionDetails(targetUserId, user, secureQuery, logDataAccess) {
  // 验证权限
  if (!hasPermission(user.role, PERMISSIONS.PLATFORM_USER_PERMISSION_VIEW) &&
      user._id !== targetUserId) {
    throw new APIError('权限不足', ERROR_CODES.PERMISSION_DENIED);
  }

  // 记录权限查询
  await logDataAccess({
    userId: user._id,
    tenantId: user.tenant_id,
    action: 'VIEW_USER_PERMISSIONS',
    dataType: 'USER_PERMISSIONS',
    targetUserId,
    timestamp: new Date()
  });

  // 获取目标用户信息
  const targetUser = await secureQuery('users', {
    _id: targetUserId
  }, { limit: 1 });

  if (targetUser.data.length === 0) {
    throw new APIError('用户不存在', ERROR_CODES.USER_NOT_FOUND);
  }

  const userData = targetUser.data[0];

  // 获取用户权限
  const userPermissions = await getUserPermissions(userData, secureQuery);

  // 获取所有可用权限用于对比
  const allPermissions = getAllPermissions();

  return {
    user: {
      id: userData._id,
      name: userData.nickname || userData.openid,
      role: userData.role,
      tenantId: userData.tenant_id,
      status: userData.status
    },
    permissions: {
      granted: userPermissions.permissions,
      denied: allPermissions.filter(p => !userPermissions.permissions.includes(p))
    },
    roleInfo: {
      roleName: userData.role,
      roleDescription: getRoleDescription(userData.role)
    },
    lastUpdated: new Date().toISOString()
  };
}

// ========== 辅助方法 ==========

async function getUserPermissions(user, secureQuery) {
  // 获取角色默认权限
  const defaultPermissions = getDefaultRolePermissions(user.role);
  
  // 获取用户特殊权限配置（如果存在）
  const userPermissionConfig = await secureQuery('user_permissions', {
    user_id: user._id
  }, { limit: 1 });

  let permissions = defaultPermissions;

  if (userPermissionConfig.data.length > 0) {
    const config = userPermissionConfig.data[0];
    // 合并默认权限和特殊权限
    permissions = [
      ...defaultPermissions,
      ...config.additional_permissions
    ].filter((permission, index, self) => 
      self.indexOf(permission) === index // 去重
    );

    // 移除被撤销的权限
    if (config.revoked_permissions) {
      permissions = permissions.filter(p => 
        !config.revoked_permissions.includes(p)
      );
    }
  }

  return {
    permissions,
    lastUpdated: new Date().toISOString()
  };
}

function getDefaultRolePermissions(role) {
  // 这里应该返回角色的默认权限列表
  // 实际实现中应该从配置文件或数据库中读取
  const rolePermissions = {
    'super_admin': Object.values(PERMISSIONS),
    'platform_admin': [
      PERMISSIONS.PLATFORM_DASHBOARD_VIEW,
      PERMISSIONS.PLATFORM_TENANT_MANAGE,
      PERMISSIONS.PLATFORM_USER_MANAGE,
      PERMISSIONS.PLATFORM_ANALYTICS_VIEW,
      PERMISSIONS.PLATFORM_AUDIT_LOG_VIEW
    ],
    'tenant_admin': [
      PERMISSIONS.TENANT_DASHBOARD_VIEW,
      PERMISSIONS.TENANT_USER_MANAGE,
      PERMISSIONS.TENANT_ANALYTICS_VIEW
    ],
    'user': [
      PERMISSIONS.USER_PROFILE_VIEW,
      PERMISSIONS.USER_PROFILE_EDIT
    ]
  };

  return rolePermissions[role] || [];
}

function getRoleDescription(role) {
  const descriptions = {
    'super_admin': '超级管理员 - 拥有所有权限',
    'platform_admin': '平台管理员 - 管理平台级别功能',
    'tenant_admin': '租户管理员 - 管理租户内部功能',
    'user': '普通用户 - 基础功能使用权限'
  };

  return descriptions[role] || '未知角色';
}

function isSensitiveOperation(action, resourceType) {
  const sensitiveActions = [
    'DELETE_USER',
    'DELETE_TENANT',
    'MODIFY_PERMISSIONS',
    'EXPORT_DATA',
    'BACKUP_SYSTEM',
    'MODIFY_SYSTEM_CONFIG'
  ];

  const sensitiveResources = [
    'USER_ACCOUNT',
    'TENANT_CONFIG',
    'SYSTEM_CONFIG',
    'PERMISSION_SYSTEM',
    'PAYMENT_INFO'
  ];

  return sensitiveActions.includes(action) || 
         sensitiveResources.includes(resourceType);
}

async function recordSecurityAuditLog(logEntry, secureQuery) {
  try {
    const auditEntry = {
      ...logEntry,
      log_type: 'security_audit',
      risk_level: calculateRiskLevel(logEntry),
      audit_timestamp: new Date()
    };

    await secureQuery.insert('security_audit_logs', auditEntry);
  } catch (error) {
    console.error('记录安全审计日志失败:', error);
  }
}

function calculateRiskLevel(logEntry) {
  // 基于操作类型、结果、时间等因素计算风险等级
  let riskScore = 0;

  // 操作类型风险评分
  if (logEntry.action.includes('DELETE')) riskScore += 3;
  if (logEntry.action.includes('MODIFY')) riskScore += 2;
  if (logEntry.action.includes('EXPORT')) riskScore += 2;

  // 操作结果风险评分
  if (logEntry.operation_result === 'failed') riskScore += 2;

  // 时间风险评分（非工作时间操作风险较高）
  const hour = logEntry.hour;
  if (hour < 8 || hour > 18) riskScore += 1;

  // 返回风险等级
  if (riskScore >= 5) return 'high';
  if (riskScore >= 3) return 'medium';
  return 'low';
}

async function checkForSecurityAlerts(logEntry, user, secureQuery) {
  try {
    // 检查短时间内的重复失败操作
    const recentFailures = await secureQuery('operation_logs', {
      user_id: user._id,
      operation_result: 'failed',
      timestamp: { 
        $gte: new Date(Date.now() - 10 * 60 * 1000) // 最近10分钟
      }
    });

    if (recentFailures.data.length >= 5) {
      // 创建安全告警
      await secureQuery.insert('security_alerts', {
        type: 'repeated_failures',
        severity: 'warning',
        user_id: user._id,
        tenant_id: user.tenant_id,
        title: '用户多次操作失败',
        message: `用户 ${user.nickname || user.openid} 在10分钟内失败操作${recentFailures.data.length}次`,
        created_time: new Date(),
        status: 'active'
      });
    }

    // 检查异常时间操作
    const hour = new Date().getHours();
    if ((hour < 6 || hour > 22) && isSensitiveOperation(logEntry.action, logEntry.resource_type)) {
      await secureQuery.insert('security_alerts', {
        type: 'unusual_time_operation',
        severity: 'info',
        user_id: user._id,
        tenant_id: user.tenant_id,
        title: '非工作时间敏感操作',
        message: `用户 ${user.nickname || user.openid} 在 ${hour}:00 执行敏感操作 ${logEntry.action}`,
        created_time: new Date(),
        status: 'active'
      });
    }

  } catch (error) {
    console.error('安全告警检查失败:', error);
  }
}

async function checkResourceAccess(user, resourceType, resourceId, hasBasicPermission, secureQuery) {
  // 基础权限检查已通过的情况下，检查资源级权限
  if (!hasBasicPermission) {
    return { granted: false, reason: '缺少基础权限' };
  }

  try {
    switch (resourceType) {
      case 'TENANT':
        // 检查租户访问权限
        if (hasPermission(user.role, PERMISSIONS.PLATFORM_ADMIN)) {
          return { granted: true, reason: '平台管理员权限' };
        }
        return { granted: user.tenant_id === resourceId, reason: '租户隔离检查' };

      case 'USER':
        // 检查用户访问权限
        const targetUser = await secureQuery('users', { _id: resourceId }, { limit: 1 });
        if (targetUser.data.length === 0) {
          return { granted: false, reason: '用户不存在' };
        }
        
        if (hasPermission(user.role, PERMISSIONS.PLATFORM_ADMIN)) {
          return { granted: true, reason: '平台管理员权限' };
        }
        
        return { 
          granted: user.tenant_id === targetUser.data[0].tenant_id, 
          reason: '租户数据隔离检查' 
        };

      default:
        return { granted: true, reason: '无特殊资源级权限要求' };
    }
  } catch (error) {
    return { granted: false, reason: `资源访问检查失败: ${error.message}` };
  }
}

async function generateSecuritySummaryReport(startDate, endDate, secureQuery) {
  // 获取统计数据
  const totalLogs = await secureQuery.count('operation_logs', {
    timestamp: { $gte: startDate, $lte: endDate }
  });

  const failedOperations = await secureQuery.count('operation_logs', {
    timestamp: { $gte: startDate, $lte: endDate },
    operation_result: 'failed'
  });

  const sensitiveOperations = await secureQuery('operation_logs', {
    timestamp: { $gte: startDate, $lte: endDate },
    action: { $in: ['DELETE_USER', 'DELETE_TENANT', 'MODIFY_PERMISSIONS'] }
  });

  const securityAlerts = await secureQuery('security_alerts', {
    created_time: { $gte: startDate, $lte: endDate }
  });

  return {
    summary: {
      totalOperations: totalLogs.count,
      failedOperations: failedOperations.count,
      successRate: ((totalLogs.count - failedOperations.count) / totalLogs.count * 100).toFixed(2) + '%',
      sensitiveOperationsCount: sensitiveOperations.data.length,
      securityAlertsCount: securityAlerts.data.length
    },
    topActions: await getTopActions(startDate, endDate, secureQuery),
    topUsers: await getTopUsers(startDate, endDate, secureQuery),
    riskAnalysis: {
      highRiskOperations: sensitiveOperations.data.length,
      failureRate: (failedOperations.count / totalLogs.count * 100).toFixed(2) + '%',
      alertTrend: 'stable' // 这里可以添加更复杂的趋势分析
    }
  };
}

async function generateDetailedSecurityReport(startDate, endDate, secureQuery) {
  // 获取详细的安全操作记录
  const securityLogs = await secureQuery('security_audit_logs', {
    audit_timestamp: { $gte: startDate, $lte: endDate }
  }, { sort: { audit_timestamp: -1 } });

  return {
    detailedLogs: securityLogs.data,
    analysis: {
      totalSecurityEvents: securityLogs.data.length,
      riskLevelDistribution: calculateRiskDistribution(securityLogs.data),
      timeDistribution: calculateTimeDistribution(securityLogs.data),
      userDistribution: calculateUserDistribution(securityLogs.data)
    }
  };
}

async function generateAnomalyDetectionReport(startDate, endDate, secureQuery) {
  // 异常检测分析
  const anomalies = [];

  // 检测异常登录时间
  const unusualTimeLogins = await secureQuery('operation_logs', {
    timestamp: { $gte: startDate, $lte: endDate },
    action: 'USER_LOGIN',
    hour: { $lt: 6, $gt: 22 }
  });

  if (unusualTimeLogins.data.length > 0) {
    anomalies.push({
      type: 'unusual_login_time',
      description: '非正常时间登录',
      count: unusualTimeLogins.data.length,
      severity: 'medium'
    });
  }

  // 检测重复失败操作
  const repeatedFailures = await secureQuery.aggregate('operation_logs', [
    { $match: { 
      timestamp: { $gte: startDate, $lte: endDate },
      operation_result: 'failed'
    }},
    { $group: { 
      _id: { user_id: '$user_id', action: '$action' },
      count: { $sum: 1 }
    }},
    { $match: { count: { $gte: 5 } }}
  ]);

  if (repeatedFailures.data.length > 0) {
    anomalies.push({
      type: 'repeated_failures',
      description: '重复操作失败',
      count: repeatedFailures.data.length,
      severity: 'high'
    });
  }

  return {
    anomalies,
    summary: {
      totalAnomalies: anomalies.length,
      highSeverityCount: anomalies.filter(a => a.severity === 'high').length,
      recommendations: generateSecurityRecommendations(anomalies)
    }
  };
}

async function getTopActions(startDate, endDate, secureQuery) {
  const topActions = await secureQuery.aggregate('operation_logs', [
    { $match: { timestamp: { $gte: startDate, $lte: endDate } }},
    { $group: { _id: '$action', count: { $sum: 1 } }},
    { $sort: { count: -1 } },
    { $limit: 10 }
  ]);

  return topActions.data;
}

async function getTopUsers(startDate, endDate, secureQuery) {
  const topUsers = await secureQuery.aggregate('operation_logs', [
    { $match: { timestamp: { $gte: startDate, $lte: endDate } }},
    { $group: { 
      _id: '$user_id', 
      count: { $sum: 1 },
      user_name: { $first: '$user_name' }
    }},
    { $sort: { count: -1 } },
    { $limit: 10 }
  ]);

  return topUsers.data;
}

function calculateRiskDistribution(logs) {
  const distribution = { low: 0, medium: 0, high: 0 };
  logs.forEach(log => {
    distribution[log.risk_level]++;
  });
  return distribution;
}

function calculateTimeDistribution(logs) {
  const hours = Array(24).fill(0);
  logs.forEach(log => {
    const hour = new Date(log.audit_timestamp).getHours();
    hours[hour]++;
  });
  return hours;
}

function calculateUserDistribution(logs) {
  const userCounts = {};
  logs.forEach(log => {
    const userId = log.user_id;
    userCounts[userId] = (userCounts[userId] || 0) + 1;
  });
  return userCounts;
}

function generateSecurityRecommendations(anomalies) {
  const recommendations = [];

  if (anomalies.some(a => a.type === 'unusual_login_time')) {
    recommendations.push('建议加强非工作时间的访问监控和二次验证');
  }

  if (anomalies.some(a => a.type === 'repeated_failures')) {
    recommendations.push('建议实施账户锁定机制，防止暴力破解攻击');
  }

  return recommendations;
}

// ========== 云函数入口 ==========

exports.main = apiHandler(async (params, context) => {
  const { action, data = {} } = params;
  const { user, secureQuery, logDataAccess } = context;

  switch (action) {
    case 'logUserOperation':
      return await logUserOperation(data, user, secureQuery, logDataAccess);
    
    case 'verifyUserPermission':
      return await verifyUserPermission(data, user, secureQuery, logDataAccess);
    
    case 'getOperationLogs':
      return await getOperationLogs(data, user, secureQuery, logDataAccess);
    
    case 'getSecurityAuditReport':
      return await getSecurityAuditReport(data, user, secureQuery, logDataAccess);
    
    case 'getUserPermissionDetails':
      return await getUserPermissionDetails(data.userId, user, secureQuery, logDataAccess);
    
    default:
      throw new APIError('不支持的操作', ERROR_CODES.UNSUPPORTED_ACTION);
  }
});