/**
 * 实时系统健康监控云函数
 * Real-time System Health Monitoring Cloud Function
 * 
 * 基于Context7最佳实践的系统健康检查和监控
 * 支持数据库、云函数、存储、网络等全方位监控
 */

const { apiHandler } = require('../../../utils/api-handler');
const { APIError, ERROR_CODES } = require('../../../utils/api-errors');
const { hasPermission, PERMISSIONS } = require('../../../utils/role-permission');

/**
 * 系统健康检查
 */
async function performHealthCheck(user, secureQuery, logDataAccess) {
  // 验证权限
  if (!hasPermission(user.role, PERMISSIONS.PLATFORM_HEALTH_CHECK)) {
    throw new APIError('权限不足', ERROR_CODES.PERMISSION_DENIED);
  }

  const healthResults = {
    overall: 'healthy',
    lastCheck: new Date().toISOString(),
    components: {},
    details: {},
    alerts: []
  };

  try {
    // 并行执行所有健康检查
    const [
      databaseHealth,
      cloudFunctionHealth,
      storageHealth,
      networkHealth,
      cacheHealth
    ] = await Promise.all([
      checkDatabaseHealth(secureQuery),
      checkCloudFunctionHealth(),
      checkStorageHealth(),
      checkNetworkHealth(),
      checkCacheHealth()
    ]);

    healthResults.components = {
      database: databaseHealth.status,
      cloudFunctions: cloudFunctionHealth.status,
      storage: storageHealth.status,
      network: networkHealth.status,
      cache: cacheHealth.status
    };

    healthResults.details = {
      database: databaseHealth.details,
      cloudFunctions: cloudFunctionHealth.details,
      storage: storageHealth.details,
      network: networkHealth.details,
      cache: cacheHealth.details
    };

    // 收集所有告警
    const allAlerts = [
      ...databaseHealth.alerts,
      ...cloudFunctionHealth.alerts,
      ...storageHealth.alerts,
      ...networkHealth.alerts,
      ...cacheHealth.alerts
    ];

    healthResults.alerts = allAlerts;

    // 计算整体健康状态
    healthResults.overall = calculateOverallHealth(healthResults.components, allAlerts);

    // 记录健康检查日志
    await logDataAccess({
      userId: user._id,
      tenantId: user.tenant_id,
      action: 'SYSTEM_HEALTH_CHECK',
      dataType: 'SYSTEM_HEALTH',
      result: healthResults.overall,
      timestamp: new Date()
    });

    // 保存健康检查结果
    await saveHealthCheckResult(healthResults, secureQuery);

    // 如果有严重问题，触发告警
    if (healthResults.overall === 'error' || allAlerts.some(a => a.severity === 'critical')) {
      await triggerHealthAlert(healthResults, user, secureQuery);
    }

    return healthResults;

  } catch (error) {
    console.error('健康检查失败:', error);
    
    healthResults.overall = 'error';
    healthResults.alerts.push({
      severity: 'critical',
      title: '健康检查执行失败',
      message: error.message,
      timestamp: new Date().toISOString()
    });

    return healthResults;
  }
}

/**
 * 数据库健康检查
 */
async function checkDatabaseHealth(secureQuery) {
  const startTime = Date.now();
  const result = {
    status: 'healthy',
    details: {},
    alerts: []
  };

  try {
    // 测试数据库连接和查询性能
    await secureQuery('system_health', {}, { limit: 1 });
    
    const queryTime = Date.now() - startTime;
    result.details.responseTime = queryTime;
    result.details.lastChecked = new Date().toISOString();

    // 检查响应时间
    if (queryTime > 1000) {
      result.status = 'warning';
      result.alerts.push({
        severity: 'warning',
        title: '数据库响应慢',
        message: `数据库查询耗时 ${queryTime}ms，超过警告阈值 1000ms`,
        timestamp: new Date().toISOString()
      });
    } else if (queryTime > 200) {
      result.status = 'warning';
      result.alerts.push({
        severity: 'info',
        title: '数据库响应时间较长',
        message: `数据库查询耗时 ${queryTime}ms`,
        timestamp: new Date().toISOString()
      });
    }

    // 检查数据库集合状态
    const collectionsStatus = await checkDatabaseCollections(secureQuery);
    result.details.collections = collectionsStatus;

    return result;

  } catch (error) {
    result.status = 'error';
    result.alerts.push({
      severity: 'critical',
      title: '数据库连接失败',
      message: error.message,
      timestamp: new Date().toISOString()
    });
    
    return result;
  }
}

/**
 * 云函数健康检查
 */
async function checkCloudFunctionHealth() {
  const result = {
    status: 'healthy',
    details: {},
    alerts: []
  };

  try {
    const functions = [
      'userManagement',
      'flockManagement', 
      'adminManagement',
      'systemManagement',
      'tenantManagement'
    ];

    const functionHealths = await Promise.allSettled(
      functions.map(func => testCloudFunction(func))
    );

    let healthyCount = 0;
    const functionDetails = {};

    functionHealths.forEach((health, index) => {
      const funcName = functions[index];
      
      if (health.status === 'fulfilled') {
        functionDetails[funcName] = {
          status: 'healthy',
          responseTime: health.value.responseTime,
          lastChecked: new Date().toISOString()
        };
        healthyCount++;
      } else {
        functionDetails[funcName] = {
          status: 'error',
          error: health.reason.message,
          lastChecked: new Date().toISOString()
        };
        
        result.alerts.push({
          severity: 'error',
          title: `云函数 ${funcName} 异常`,
          message: health.reason.message,
          timestamp: new Date().toISOString()
        });
      }
    });

    result.details = {
      totalFunctions: functions.length,
      healthyFunctions: healthyCount,
      functions: functionDetails
    };

    // 计算整体状态
    const healthRate = healthyCount / functions.length;
    if (healthRate < 0.5) {
      result.status = 'error';
    } else if (healthRate < 0.8) {
      result.status = 'warning';
    }

    return result;

  } catch (error) {
    result.status = 'error';
    result.alerts.push({
      severity: 'critical',
      title: '云函数健康检查失败',
      message: error.message,
      timestamp: new Date().toISOString()
    });
    
    return result;
  }
}

/**
 * 存储健康检查
 */
async function checkStorageHealth() {
  const result = {
    status: 'healthy',
    details: {},
    alerts: []
  };

  try {
    // 模拟存储检查（在实际环境中需要调用具体的存储API）
    const storageInfo = {
      totalSpace: 100 * 1024 * 1024 * 1024, // 100GB
      usedSpace: 85 * 1024 * 1024 * 1024,   // 85GB
      freeSpace: 15 * 1024 * 1024 * 1024    // 15GB
    };

    const usageRate = storageInfo.usedSpace / storageInfo.totalSpace;
    
    result.details = {
      usageRate: Math.round(usageRate * 100),
      totalSpace: formatBytes(storageInfo.totalSpace),
      usedSpace: formatBytes(storageInfo.usedSpace),
      freeSpace: formatBytes(storageInfo.freeSpace),
      lastChecked: new Date().toISOString()
    };

    // 检查存储使用率
    if (usageRate > 0.95) {
      result.status = 'error';
      result.alerts.push({
        severity: 'critical',
        title: '存储空间严重不足',
        message: `存储使用率已达 ${Math.round(usageRate * 100)}%，请立即清理或扩容`,
        timestamp: new Date().toISOString()
      });
    } else if (usageRate > 0.85) {
      result.status = 'warning';
      result.alerts.push({
        severity: 'warning',
        title: '存储空间不足',
        message: `存储使用率已达 ${Math.round(usageRate * 100)}%，建议清理不必要文件`,
        timestamp: new Date().toISOString()
      });
    }

    return result;

  } catch (error) {
    result.status = 'error';
    result.alerts.push({
      severity: 'error',
      title: '存储健康检查失败',
      message: error.message,
      timestamp: new Date().toISOString()
    });
    
    return result;
  }
}

/**
 * 网络健康检查
 */
async function checkNetworkHealth() {
  const result = {
    status: 'healthy',
    details: {},
    alerts: []
  };

  try {
    // 检查网络延迟
    const startTime = Date.now();
    
    // 模拟网络请求（实际环境中可能需要ping外部服务）
    await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
    
    const latency = Date.now() - startTime;
    
    result.details = {
      latency,
      lastChecked: new Date().toISOString(),
      status: 'connected'
    };

    if (latency > 500) {
      result.status = 'warning';
      result.alerts.push({
        severity: 'warning',
        title: '网络延迟较高',
        message: `网络延迟 ${latency}ms，可能影响服务响应速度`,
        timestamp: new Date().toISOString()
      });
    }

    return result;

  } catch (error) {
    result.status = 'error';
    result.alerts.push({
      severity: 'error',
      title: '网络健康检查失败',
      message: error.message,
      timestamp: new Date().toISOString()
    });
    
    return result;
  }
}

/**
 * 缓存健康检查
 */
async function checkCacheHealth() {
  const result = {
    status: 'healthy',
    details: {},
    alerts: []
  };

  try {
    // 模拟缓存检查
    const cacheStats = {
      hitRate: 0.85,
      totalRequests: 10000,
      hits: 8500,
      misses: 1500,
      lastCleared: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
    };

    result.details = {
      ...cacheStats,
      lastChecked: new Date().toISOString()
    };

    if (cacheStats.hitRate < 0.6) {
      result.status = 'warning';
      result.alerts.push({
        severity: 'warning',
        title: '缓存命中率低',
        message: `缓存命中率仅为 ${Math.round(cacheStats.hitRate * 100)}%，建议优化缓存策略`,
        timestamp: new Date().toISOString()
      });
    }

    return result;

  } catch (error) {
    result.status = 'error';
    result.alerts.push({
      severity: 'error',
      title: '缓存健康检查失败',
      message: error.message,
      timestamp: new Date().toISOString()
    });
    
    return result;
  }
}

/**
 * 获取系统监控指标
 */
async function getSystemMetrics(params, user, secureQuery, logDataAccess) {
  // 验证权限
  if (!hasPermission(user.role, PERMISSIONS.PLATFORM_MONITORING_VIEW)) {
    throw new APIError('权限不足', ERROR_CODES.PERMISSION_DENIED);
  }

  const { timeRange = '1h', metrics = ['cpu', 'memory', 'network', 'requests'] } = params;

  // 记录访问
  await logDataAccess({
    userId: user._id,
    tenantId: user.tenant_id,
    action: 'GET_SYSTEM_METRICS',
    dataType: 'SYSTEM_METRICS',
    params: { timeRange, metrics },
    timestamp: new Date()
  });

  const endTime = new Date();
  const startTime = new Date();

  switch (timeRange) {
    case '1h':
      startTime.setHours(endTime.getHours() - 1);
      break;
    case '6h':
      startTime.setHours(endTime.getHours() - 6);
      break;
    case '24h':
      startTime.setDate(endTime.getDate() - 1);
      break;
    case '7d':
      startTime.setDate(endTime.getDate() - 7);
      break;
  }

  const metricsData = await secureQuery('system_metrics', {
    timestamp: { $gte: startTime, $lte: endTime },
    metric: { $in: metrics }
  }, { sort: { timestamp: 1 } });

  return {
    timeRange,
    startTime: startTime.toISOString(),
    endTime: endTime.toISOString(),
    metrics: organizeMetricsData(metricsData.data),
    summary: calculateMetricsSummary(metricsData.data)
  };
}

// ========== 辅助方法 ==========

function calculateOverallHealth(components, alerts) {
  const componentStatuses = Object.values(components);
  
  // 如果有任何组件处于错误状态
  if (componentStatuses.includes('error')) {
    return 'error';
  }
  
  // 如果有警告状态或严重告警
  if (componentStatuses.includes('warning') || 
      alerts.some(a => a.severity === 'critical' || a.severity === 'error')) {
    return 'warning';
  }
  
  return 'healthy';
}

async function checkDatabaseCollections(secureQuery) {
  const collections = ['users', 'flocks', 'tenants', 'system_config'];
  const status = {};
  
  for (const collection of collections) {
    try {
      const result = await secureQuery(collection, {}, { limit: 1 });
      status[collection] = {
        status: 'healthy',
        accessible: true,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      status[collection] = {
        status: 'error',
        accessible: false,
        error: error.message,
        lastChecked: new Date().toISOString()
      };
    }
  }
  
  return status;
}

async function testCloudFunction(functionName) {
  // 模拟云函数测试
  const startTime = Date.now();
  
  // 在实际环境中，这里应该调用对应的云函数进行健康检查
  await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
  
  const responseTime = Date.now() - startTime;
  
  if (Math.random() > 0.95) { // 5% 的失败率用于模拟
    throw new Error(`云函数 ${functionName} 响应超时`);
  }
  
  return { responseTime };
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function organizeMetricsData(rawData) {
  const organized = {};
  
  rawData.forEach(point => {
    if (!organized[point.metric]) {
      organized[point.metric] = [];
    }
    
    organized[point.metric].push({
      timestamp: point.timestamp,
      value: point.value,
      unit: point.unit || ''
    });
  });
  
  return organized;
}

function calculateMetricsSummary(rawData) {
  const summary = {};
  
  rawData.forEach(point => {
    if (!summary[point.metric]) {
      summary[point.metric] = {
        count: 0,
        sum: 0,
        min: Infinity,
        max: -Infinity,
        avg: 0
      };
    }
    
    const metric = summary[point.metric];
    metric.count++;
    metric.sum += point.value;
    metric.min = Math.min(metric.min, point.value);
    metric.max = Math.max(metric.max, point.value);
    metric.avg = metric.sum / metric.count;
  });
  
  return summary;
}

async function saveHealthCheckResult(healthResult, secureQuery) {
  try {
    await secureQuery.insert('system_health_history', {
      ...healthResult,
      saved_at: new Date()
    });
  } catch (error) {
    console.error('保存健康检查结果失败:', error);
  }
}

async function triggerHealthAlert(healthResult, user, secureQuery) {
  try {
    const criticalAlerts = healthResult.alerts.filter(
      alert => alert.severity === 'critical' || alert.severity === 'error'
    );

    if (criticalAlerts.length > 0) {
      // 创建系统告警记录
      await secureQuery.insert('system_alerts', {
        type: 'system_health',
        severity: 'critical',
        title: '系统健康检查发现严重问题',
        message: `发现 ${criticalAlerts.length} 个严重问题，需要立即处理`,
        details: criticalAlerts,
        created_by: user._id,
        created_time: new Date(),
        status: 'active'
      });

      // 这里可以添加通知逻辑，如发送邮件、短信等
      console.log('系统健康告警已触发:', criticalAlerts);
    }
  } catch (error) {
    console.error('触发健康告警失败:', error);
  }
}

// ========== 云函数入口 ==========

exports.main = apiHandler(async (params, context) => {
  const { action, data = {} } = params;
  const { user, secureQuery, logDataAccess } = context;

  switch (action) {
    case 'performHealthCheck':
      return await performHealthCheck(user, secureQuery, logDataAccess);
    
    case 'getSystemMetrics':
      return await getSystemMetrics(data, user, secureQuery, logDataAccess);
    
    default:
      throw new APIError('不支持的操作', ERROR_CODES.UNSUPPORTED_ACTION);
  }
});