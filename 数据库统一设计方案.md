# 智慧养鹅SAAS平台统一数据库设计方案

## 🎯 设计目标

1. **严格数据隔离**：确保租户间数据完全隔离
2. **平台级统一管理**：支持全平台功能统一配置
3. **高性能查询**：优化索引设计，支持高并发访问
4. **扩展性良好**：支持新租户快速接入
5. **数据一致性**：保证关键业务数据的完整性

---

## 📊 数据库架构设计

### 1. 数据分层架构

```
数据库分层架构：
├── 平台层 (Platform Layer)
│   ├── 系统管理数据
│   ├── 租户管理数据
│   ├── 全局配置数据
│   └── 平台级业务数据
├── 租户层 (Tenant Layer)  
│   ├── 业务核心数据 (tenant_id隔离)
│   ├── 业务流程数据
│   └── 租户配置数据
├── 共享层 (Shared Layer)
│   ├── 知识库数据
│   ├── 商城数据
│   └── 工作空间数据
└── 用户层 (User Layer)
    ├── 用户个人数据
    ├── 偏好设置数据
    └── 会话数据
```

### 2. 集合设计规范

#### 命名规范
- **平台级集合**：`platform_*` 前缀
- **租户级集合**：无前缀，通过 `tenant_id` 字段隔离
- **共享级集合**：`shared_*` 前缀
- **用户级集合**：`user_*` 前缀

#### 字段命名规范
- **主键**：`_id` (微信云开发标准)
- **租户标识**：`tenant_id` (String, 必须)
- **用户标识**：`user_id` (String)
- **时间字段**：`*_time` (Date类型)
- **状态字段**：`status` (String)
- **标志字段**：`is_*` (Boolean)

---

## 🗄️ 核心集合设计

### 平台级集合（Platform Level）

#### 1. 租户管理集合 (platform_tenants)
```javascript
{
  _id: ObjectId,
  tenant_id: String,           // 租户唯一标识
  tenant_name: String,         // 租户名称
  domain: String,              // 子域名(可选)
  subscription_plan: String,    // 订阅计划 (basic/standard/premium)
  subscription_status: String, // 订阅状态 (active/inactive/expired)
  features: Array,             // 可用功能列表
  limits: {                    // 限制配置
    max_users: Number,
    max_flocks: Number,
    max_storage_mb: Number
  },
  admin_user_id: String,       // 租户管理员ID
  contact_info: {              // 联系信息
    company: String,
    contact_person: String,
    phone: String,
    email: String,
    address: String
  },
  billing_info: {              // 计费信息
    plan_price: Number,
    billing_cycle: String,     // monthly/yearly
    next_billing_date: Date
  },
  status: String,              // active/suspended/deleted
  created_time: Date,
  updated_time: Date,
  expire_time: Date
}
```

#### 2. 今日鹅价集合 (platform_goose_prices)
```javascript
{
  _id: ObjectId,
  price_date: Date,            // 价格日期
  region: String,              // 地区 (华北/华南/华东等)
  breed: String,               // 品种 (白鹅/灰鹅/黑鹅)
  price_per_kg: Number,        // 每公斤价格
  price_trend: String,         // 价格趋势 (up/down/stable)
  trend_percentage: Number,    // 涨跌幅度
  market_analysis: String,     // 市场分析
  source: String,              // 数据来源
  publisher_id: String,        // 发布者ID
  status: String,              // active/inactive
  created_time: Date,
  updated_time: Date
}
```

#### 3. 平台公告集合 (platform_announcements)
```javascript
{
  _id: ObjectId,
  title: String,               // 公告标题
  content: String,             // 公告内容
  announcement_type: String,   // 公告类型 (system/maintenance/feature)
  priority: String,            // 优先级 (high/medium/low)
  target_tenants: Array,       // 目标租户列表 (空则全部)
  target_roles: Array,         // 目标角色列表
  is_pinned: Boolean,          // 是否置顶
  read_count: Number,          // 阅读次数
  publisher_id: String,        // 发布者ID
  status: String,              // draft/published/archived
  publish_time: Date,          // 发布时间
  expire_time: Date,           // 过期时间
  created_time: Date,
  updated_time: Date
}
```

#### 4. AI模型配置集合 (platform_ai_configs)
```javascript
{
  _id: ObjectId,
  model_name: String,          // 模型名称
  model_type: String,          // 模型类型 (health_diagnosis/price_prediction)
  api_provider: String,        // API提供商 (openai/baidu/aliyun)
  api_endpoint: String,        // API端点
  api_key: String,            // API密钥 (加密存储)
  model_version: String,       // 模型版本
  parameters: {                // 模型参数
    temperature: Number,
    max_tokens: Number,
    top_p: Number
  },
  usage_limits: {              // 使用限制
    daily_requests: Number,
    monthly_requests: Number
  },
  is_default: Boolean,         // 是否默认模型
  status: String,              // active/inactive/testing
  created_time: Date,
  updated_time: Date
}
```

### 租户级集合（Tenant Level）

#### 5. 鹅群管理集合 (flocks)
```javascript
{
  _id: ObjectId,
  tenant_id: String,           // 租户ID - 必须字段
  flock_code: String,          // 鹅群编号
  flock_name: String,          // 鹅群名称
  breed: String,               // 品种
  breed_details: {             // 品种详情
    breed_type: String,
    origin: String,
    characteristics: String
  },
  quantity: {                  // 数量信息
    initial_count: Number,     // 初始数量
    current_count: Number,     // 当前数量
    male_count: Number,        // 公鹅数量
    female_count: Number       // 母鹅数量
  },
  age_info: {                  // 年龄信息
    hatch_date: Date,          // 孵化日期
    age_weeks: Number,         // 周龄
    growth_stage: String       // 成长阶段 (chick/juvenile/adult/breeder)
  },
  location: {                  // 位置信息
    building: String,          // 栏舍
    section: String,           // 区域
    coordinates: {             // 坐标
      latitude: Number,
      longitude: Number
    }
  },
  health_status: String,       // 健康状态 (healthy/sick/quarantine)
  manager_id: String,          // 负责人ID
  notes: String,               // 备注
  is_active: Boolean,          // 是否活跃
  created_by: String,          // 创建人
  created_time: Date,
  updated_by: String,          // 更新人
  updated_time: Date
}
```

#### 6. 健康记录集合 (health_records)
```javascript
{
  _id: ObjectId,
  tenant_id: String,           // 租户ID - 必须字段
  flock_id: String,            // 鹅群ID
  record_type: String,         // 记录类型 (vaccination/treatment/checkup/death)
  record_date: Date,           // 记录日期
  details: {
    // 疫苗接种记录
    vaccine: {
      vaccine_name: String,
      batch_number: String,
      dosage: String,
      vaccination_method: String,
      next_vaccination_date: Date
    },
    // 治疗记录
    treatment: {
      symptoms: String,
      diagnosis: String,
      medication: Array,        // 药物列表
      treatment_method: String,
      follow_up_required: Boolean,
      follow_up_date: Date
    },
    // 健康检查记录
    checkup: {
      weight_avg: Number,       // 平均体重
      temperature_avg: Number,  // 平均体温
      appetite_status: String,  // 食欲状态
      activity_level: String,   // 活动水平
      abnormal_signs: String    // 异常症状
    },
    // 死亡记录
    death: {
      death_count: Number,      // 死亡数量
      cause: String,           // 死亡原因
      age_at_death: Number,    // 死亡时周龄
      disposal_method: String   // 处理方式
    }
  },
  veterinarian_id: String,     // 兽医ID
  cost: Number,                // 费用
  images: Array,               // 相关图片
  status: String,              // pending/completed/cancelled
  created_by: String,
  created_time: Date,
  updated_by: String,
  updated_time: Date
}
```

#### 7. 生产物料集合 (materials)
```javascript
{
  _id: ObjectId,
  tenant_id: String,           // 租户ID - 必须字段
  material_code: String,       // 物料编码
  material_name: String,       // 物料名称
  material_type: String,       // 物料类型 (feed/medicine/equipment/tools)
  category: String,            // 分类
  specifications: {            // 规格信息
    brand: String,             // 品牌
    model: String,             // 型号
    specification: String,     // 规格
    unit: String               // 单位
  },
  inventory: {                 // 库存信息
    current_stock: Number,     // 当前库存
    min_stock: Number,         // 最低库存
    max_stock: Number,         // 最高库存
    reorder_point: Number,     // 再订货点
    location: String           // 存放位置
  },
  cost_info: {                 // 成本信息
    unit_price: Number,        // 单价
    total_value: Number,       // 总价值
    last_purchase_price: Number, // 最近采购价
    average_cost: Number       // 平均成本
  },
  supplier_info: {             // 供应商信息
    supplier_name: String,
    contact_person: String,
    phone: String,
    address: String
  },
  quality_info: {              // 质量信息
    production_date: Date,     // 生产日期
    expiry_date: Date,         // 过期日期
    batch_number: String,      // 批次号
    quality_grade: String      // 质量等级
  },
  usage_records: Array,        // 使用记录
  status: String,              // active/inactive/expired
  created_by: String,
  created_time: Date,
  updated_by: String,
  updated_time: Date
}
```

#### 8. 财务记录集合 (financial_records)
```javascript
{
  _id: ObjectId,
  tenant_id: String,           // 租户ID - 必须字段
  record_number: String,       // 记录编号
  record_type: String,         // 记录类型 (income/expense)
  transaction_date: Date,      // 交易日期
  category: String,            // 分类
  subcategory: String,         // 子分类
  amount: Number,              // 金额
  description: String,         // 描述
  details: {
    // 收入详情
    income: {
      source: String,          // 收入来源
      customer: String,        // 客户
      product: String,         // 产品
      quantity: Number,        // 数量
      unit_price: Number       // 单价
    },
    // 支出详情
    expense: {
      expense_type: String,    // 支出类型
      supplier: String,        // 供应商
      items: Array,           // 支出项目
      tax_amount: Number       // 税额
    }
  },
  related_records: {           // 关联记录
    flock_id: String,         // 关联鹅群
    material_id: String,      // 关联物料
    order_id: String,         // 关联订单
    contract_id: String       // 关联合同
  },
  payment_info: {              // 支付信息
    payment_method: String,   // 支付方式
    bank_account: String,     // 银行账户
    reference_number: String  // 参考号
  },
  approval_info: {             // 审批信息
    requires_approval: Boolean,
    approved_by: String,
    approval_date: Date,
    approval_status: String   // pending/approved/rejected
  },
  attachments: Array,          // 附件
  operator_id: String,         // 操作员ID
  status: String,              // draft/confirmed/cancelled
  created_by: String,
  created_time: Date,
  updated_by: String,
  updated_time: Date
}
```

### 共享级集合（Shared Level）

#### 9. 知识库集合 (shared_knowledge_base)
```javascript
{
  _id: ObjectId,
  title: String,               // 标题
  content: String,             // 内容
  category: String,            // 分类
  subcategory: String,         // 子分类
  tags: Array,                 // 标签
  visibility: String,          // 可见性 (public/tenant/private)
  tenant_id: String,           // 租户ID (private/tenant级别时必须)
  author_id: String,           // 作者ID
  content_type: String,        // 内容类型 (article/video/document/faq)
  difficulty_level: String,    // 难度级别 (beginner/intermediate/advanced)
  estimated_reading_time: Number, // 预计阅读时间(分钟)
  attachments: Array,          // 附件
  related_articles: Array,     // 相关文章
  statistics: {                // 统计信息
    view_count: Number,
    like_count: Number,
    share_count: Number,
    comment_count: Number
  },
  seo_info: {                  // SEO信息
    meta_title: String,
    meta_description: String,
    keywords: Array
  },
  status: String,              // draft/published/archived
  is_featured: Boolean,        // 是否推荐
  publish_time: Date,
  created_by: String,
  created_time: Date,
  updated_by: String,
  updated_time: Date
}
```

#### 10. 商城商品集合 (shared_shop_products)
```javascript
{
  _id: ObjectId,
  product_code: String,        // 商品编码
  product_name: String,        // 商品名称
  category: String,            // 分类
  subcategory: String,         // 子分类
  brand: String,               // 品牌
  description: String,         // 描述
  detailed_description: String, // 详细描述
  images: Array,               // 商品图片
  specifications: Object,      // 规格信息
  pricing: {                   // 价格信息
    original_price: Number,    // 原价
    sale_price: Number,        // 售价
    discount_percentage: Number, // 折扣百分比
    bulk_pricing: Array        // 批量定价
  },
  inventory: {                 // 库存信息
    stock_quantity: Number,    // 库存数量
    reserved_quantity: Number, // 预留数量
    reorder_level: Number      // 再订货水平
  },
  shipping_info: {             // 配送信息
    weight: Number,            // 重量
    dimensions: Object,        // 尺寸
    shipping_cost: Number,     // 配送费用
    free_shipping_threshold: Number // 免费配送门槛
  },
  vendor_info: {               // 供应商信息
    vendor_id: String,
    vendor_name: String,
    vendor_rating: Number
  },
  seo_info: {                  // SEO信息
    meta_title: String,
    meta_description: String,
    keywords: Array
  },
  statistics: {                // 统计信息
    view_count: Number,
    sales_count: Number,
    rating_average: Number,
    review_count: Number
  },
  availability: {              // 可用性
    is_available: Boolean,
    available_regions: Array,  // 可配送区域
    available_for_tenants: Array // 可购买租户
  },
  status: String,              // active/inactive/discontinued
  is_featured: Boolean,        // 是否推荐
  created_by: String,
  created_time: Date,
  updated_by: String,
  updated_time: Date
}
```

### 用户级集合（User Level）

#### 11. 用户信息集合 (users)
```javascript
{
  _id: ObjectId,
  openid: String,              // 微信openid - 唯一标识
  unionid: String,             // 微信unionid
  tenant_id: String,           // 所属租户ID
  user_info: {                 // 用户基础信息
    nickname: String,          // 昵称
    real_name: String,         // 真实姓名
    avatar_url: String,        // 头像URL
    gender: Number,            // 性别 0:未知 1:男 2:女
    phone: String,             // 手机号
    email: String,             // 邮箱
    id_card: String            // 身份证号(加密)
  },
  role_info: {                 // 角色信息
    role: String,              // 用户角色
    department: String,        // 部门
    position: String,          // 职位
    level: Number,             // 级别
    permissions: Array         // 权限列表
  },
  profile_info: {              // 档案信息
    hire_date: Date,           // 入职日期
    work_experience: String,   // 工作经验
    education: String,         // 教育背景
    certificates: Array,       // 证书
    skills: Array              // 技能
  },
  preferences: {               // 偏好设置
    language: String,          // 语言
    timezone: String,          // 时区
    notification_settings: Object, // 通知设置
    theme: String              // 主题
  },
  security_info: {             // 安全信息
    password_hash: String,     // 密码哈希
    two_factor_enabled: Boolean, // 双因子认证
    last_password_change: Date,
    failed_login_attempts: Number,
    account_locked_until: Date
  },
  session_info: {              // 会话信息
    last_login_time: Date,     // 最后登录时间
    last_login_ip: String,     // 最后登录IP
    session_token: String,     // 会话令牌
    device_info: Object        // 设备信息
  },
  status: String,              // active/inactive/suspended/deleted
  is_verified: Boolean,        // 是否已验证
  created_time: Date,
  updated_time: Date
}
```

---

## 🔧 索引设计方案

### 1. 性能优化索引

```javascript
// 租户级数据的复合索引
db.flocks.createIndex({ "tenant_id": 1, "status": 1, "created_time": -1 })
db.health_records.createIndex({ "tenant_id": 1, "flock_id": 1, "record_date": -1 })
db.materials.createIndex({ "tenant_id": 1, "material_type": 1, "status": 1 })
db.financial_records.createIndex({ "tenant_id": 1, "record_type": 1, "transaction_date": -1 })

// 平台级数据索引
db.platform_tenants.createIndex({ "tenant_id": 1 }, { unique: true })
db.platform_goose_prices.createIndex({ "price_date": -1, "region": 1, "breed": 1 })
db.platform_announcements.createIndex({ "status": 1, "publish_time": -1, "priority": -1 })

// 用户相关索引
db.users.createIndex({ "openid": 1 }, { unique: true })
db.users.createIndex({ "tenant_id": 1, "role_info.role": 1, "status": 1 })

// 共享数据索引
db.shared_knowledge_base.createIndex({ "visibility": 1, "category": 1, "status": 1 })
db.shared_shop_products.createIndex({ "category": 1, "status": 1, "is_available": 1 })
```

### 2. 查询优化索引

```javascript
// 支持模糊搜索的文本索引
db.flocks.createIndex({ 
  "flock_name": "text", 
  "breed": "text",
  "notes": "text" 
})

db.shared_knowledge_base.createIndex({
  "title": "text",
  "content": "text", 
  "tags": "text"
})

// 地理位置索引
db.flocks.createIndex({ "location.coordinates": "2dsphere" })

// 支持时间范围查询的索引
db.health_records.createIndex({ "tenant_id": 1, "record_date": -1 })
db.financial_records.createIndex({ "tenant_id": 1, "transaction_date": -1, "record_type": 1 })
```

---

## 🛡️ 数据安全策略

### 1. 数据隔离规则

```javascript
// 租户级数据隔离规则
const tenantDataRule = {
  read: "auth != null && auth.tenant_id == resource.tenant_id",
  write: "auth != null && auth.tenant_id == resource.tenant_id && auth.role in ['admin', 'manager']"
}

// 用户级数据隔离规则
const userDataRule = {
  read: "auth != null && (auth._id == resource.user_id || auth.role == 'admin')",
  write: "auth != null && auth._id == resource.user_id"
}

// 平台级数据访问规则
const platformDataRule = {
  read: "auth != null && auth.role in ['super_admin', 'platform_admin']",
  write: "auth != null && auth.role == 'super_admin'"
}
```

### 2. 敏感数据加密

```javascript
// 需要加密存储的字段
const encryptedFields = [
  'users.user_info.phone',
  'users.user_info.email', 
  'users.user_info.id_card',
  'users.security_info.password_hash',
  'platform_ai_configs.api_key',
  'financial_records.payment_info.bank_account'
]

// 字段级加密示例
function encryptSensitiveData(data, fields) {
  const encrypted = { ...data }
  
  fields.forEach(field => {
    if (encrypted[field]) {
      encrypted[field] = encrypt(encrypted[field])
    }
  })
  
  return encrypted
}
```

---

## 📈 性能监控指标

### 1. 查询性能指标

```javascript
const performanceMetrics = {
  // 查询响应时间目标
  queryResponseTime: {
    simple_queries: '<50ms',      // 简单查询
    complex_queries: '<200ms',    // 复杂查询
    aggregation_queries: '<500ms' // 聚合查询
  },
  
  // 并发处理能力
  concurrency: {
    read_operations: '1000/s',    // 读操作
    write_operations: '200/s',    // 写操作
    concurrent_users: '500+'      // 并发用户
  },
  
  // 数据存储效率
  storage: {
    index_hit_ratio: '>95%',      // 索引命中率
    query_cache_hit_ratio: '>80%', // 查询缓存命中率
    storage_growth_rate: '<20%/month' // 存储增长率
  }
}
```

### 2. 监控告警规则

```javascript
const alertRules = {
  // 性能告警
  performance: {
    slow_query_threshold: '1000ms',
    high_cpu_usage: '80%',
    high_memory_usage: '85%',
    connection_pool_exhaustion: '90%'
  },
  
  // 业务告警
  business: {
    tenant_data_isolation_violation: 'immediate',
    unauthorized_access_attempt: 'immediate', 
    data_corruption_detected: 'critical',
    backup_failure: 'high'
  }
}
```

---

## 🚀 实施计划

### 第一阶段：核心集合创建（1天）
1. 创建平台级核心集合
2. 创建租户级核心集合
3. 建立基础索引
4. 配置数据隔离规则

### 第二阶段：数据迁移（1天）
1. 数据结构标准化
2. 现有数据迁移脚本
3. 数据完整性验证
4. 性能基准测试

### 第三阶段：API对接（1天）
1. 更新云函数数据访问层
2. 前端API调用适配
3. 数据隔离中间件部署
4. 端到端功能测试

---

## ✅ 验收标准

1. **数据隔离**：100%通过租户隔离测试
2. **查询性能**：95%查询响应时间<100ms
3. **索引效率**：索引命中率>95%
4. **数据一致性**：0数据不一致错误
5. **安全合规**：通过所有安全审计检查

---

*此数据库设计方案完全基于微信小程序云开发环境，确保与现有技术栈完全兼容，支持项目从95%完成度提升到100%生产就绪状态。*