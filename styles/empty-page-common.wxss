/**
 * 空页面通用样式
 * 用于开发中的页面显示
 */

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.header {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  display: block;
}

.content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
}

.empty-state {
  text-align: center;
  padding: 60rpx 40rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  display: block;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  display: block;
  line-height: 1.5;
}
