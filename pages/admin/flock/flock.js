/**
 * 鹅群管理页面
 * Flock Management Page
 * 
 * 基于租户级鹅群管理权限：
 * - FLOCK_VIEW_ALL: 查看所有鹅群数据
 * - FLOCK_VIEW_OWN: 查看自己的鹅群数据
 * - FLOCK_CREATE: 创建鹅群记录
 * - FLOCK_EDIT: 编辑鹅群记录
 * - FLOCK_DELETE: 删除鹅群记录
 * - FLOCK_STATISTICS: 鹅群统计分析
 */

const { getCurrentUserPermissions, PERMISSIONS, hasPermission } = require('../../../utils/role-permission');
const { AdminPermissionManager } = require('../../../utils/admin-permission-manager');

Page({
  data: {
    loading: false,
    userPermissions: null,
    
    // 鹅群列表数据
    flockList: [],
    
    // 当前编辑的鹅群
    currentFlock: null,
    
    // 表单数据
    formData: {
      name: '',
      code: '',
      breed: 'white_goose', // white_goose, gray_goose, lion_head_goose
      count: 0,
      age_weeks: 0,
      location: '',
      manager: '',
      status: 'active', // active, inactive, sold, deceased
      purchase_date: '',
      purchase_price: 0,
      source: '',
      vaccination_status: 'up_to_date', // up_to_date, overdue, none
      health_status: 'healthy', // healthy, sick, quarantine
      feeding_method: 'free_range', // free_range, cage, mixed
      notes: ''
    },
    
    // 筛选条件
    filters: {
      status: '',
      breed: '',
      health_status: '',
      manager: '',
      keyword: ''
    },
    
    // 页面状态
    showForm: false,
    showStatistics: false,
    showHealthRecord: false,
    isEditing: false,
    
    // 统计数据
    statistics: {
      total_flocks: 0,
      total_geese: 0,
      active_flocks: 0,
      healthy_rate: 0,
      avg_age: 0,
      total_value: 0
    },
    
    // 品种选项
    breedOptions: [
      { value: 'white_goose', label: '白鹅', description: '产蛋量高，适应性强' },
      { value: 'gray_goose', label: '灰鹅', description: '肉质鲜美，生长快速' },
      { value: 'lion_head_goose', label: '狮头鹅', description: '体型大，经济价值高' },
      { value: 'embden_goose', label: '埃姆登鹅', description: '欧洲品种，产肉性能好' },
      { value: 'toulouse_goose', label: '图卢兹鹅', description: '法国品种，适合肥肝生产' }
    ],
    
    // 状态选项
    statusOptions: [
      { value: 'active', label: '正常饲养', color: '#52c41a' },
      { value: 'inactive', label: '暂停饲养', color: '#faad14' },
      { value: 'sold', label: '已出售', color: '#1890ff' },
      { value: 'deceased', label: '死亡淘汰', color: '#f5222d' }
    ],
    
    // 健康状态选项
    healthStatusOptions: [
      { value: 'healthy', label: '健康', color: '#52c41a' },
      { value: 'sick', label: '患病', color: '#faad14' },
      { value: 'quarantine', label: '隔离', color: '#f5222d' },
      { value: 'recovering', label: '康复中', color: '#1890ff' }
    ],
    
    // 饲养方式选项
    feedingMethodOptions: [
      { value: 'free_range', label: '散养' },
      { value: 'cage', label: '笼养' },
      { value: 'mixed', label: '混合饲养' },
      { value: 'pasture', label: '牧场放养' }
    ],
    
    // 疫苗状态选项
    vaccinationOptions: [
      { value: 'up_to_date', label: '疫苗齐全', color: '#52c41a' },
      { value: 'overdue', label: '疫苗过期', color: '#faad14' },
      { value: 'none', label: '未接种', color: '#f5222d' }
    ]
  },

  async onLoad(options) {
    await this.checkPermissions();
    await this.loadFlockData();
    
    // 处理页面参数
    if (options.action === 'create') {
      this.showCreateForm();
    } else if (options.action === 'statistics') {
      this.showStatisticsView();
    }
  },

  /**
   * 检查用户权限
   */
  async checkPermissions() {
    try {
      const userPermissions = await getCurrentUserPermissions();
      
      // 检查是否有鹅群查看权限
      const canViewAll = hasPermission(userPermissions.role, PERMISSIONS.FLOCK_VIEW_ALL);
      const canViewOwn = hasPermission(userPermissions.role, PERMISSIONS.FLOCK_VIEW_OWN);
      
      if (!canViewAll && !canViewOwn) {
        wx.showModal({
          title: '权限不足',
          content: '您没有访问鹅群管理的权限',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }
      
      this.setData({
        userPermissions
      });
      
    } catch (error) {
      console.error('权限检查失败:', error);
      wx.showToast({
        title: '权限检查失败',
        icon: 'error'
      });
    }
  },

  /**
   * 加载鹅群数据
   */
  async loadFlockData() {
    try {
      this.setData({ loading: true });
      
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageFlocks',
          data: {
            operation: 'list',
            filters: this.data.filters,
            page: 1,
            limit: 50
          }
        }
      });
      
      if (result.result.success) {
        this.setData({
          flockList: result.result.data.list,
          statistics: result.result.data.statistics
        });
      } else {
        wx.showToast({
          title: result.result.error || '加载失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('加载鹅群数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 显示创建表单
   */
  showCreateForm() {
    // 检查创建权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.FLOCK_CREATE)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showForm: true,
      isEditing: false,
      currentFlock: null,
      formData: {
        name: '',
        code: this.generateFlockCode(),
        breed: 'white_goose',
        count: 0,
        age_weeks: 0,
        location: '',
        manager: '',
        status: 'active',
        purchase_date: this.getCurrentDate(),
        purchase_price: 0,
        source: '',
        vaccination_status: 'none',
        health_status: 'healthy',
        feeding_method: 'free_range',
        notes: ''
      }
    });
  },

  /**
   * 显示编辑表单
   */
  showEditForm(e) {
    const { flock } = e.currentTarget.dataset;
    
    // 检查编辑权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.FLOCK_EDIT)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showForm: true,
      isEditing: true,
      currentFlock: flock,
      formData: {
        name: flock.name,
        code: flock.code,
        breed: flock.breed,
        count: flock.count,
        age_weeks: flock.age_weeks || 0,
        location: flock.location || '',
        manager: flock.manager || '',
        status: flock.status,
        purchase_date: flock.purchase_date,
        purchase_price: flock.purchase_price || 0,
        source: flock.source || '',
        vaccination_status: flock.vaccination_status || 'none',
        health_status: flock.health_status || 'healthy',
        feeding_method: flock.feeding_method || 'free_range',
        notes: flock.notes || ''
      }
    });
  },

  /**
   * 显示统计视图
   */
  showStatisticsView() {
    // 检查统计权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.FLOCK_STATISTICS)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showStatistics: true
    });
  },

  /**
   * 显示健康记录
   */
  showHealthRecord(e) {
    const { flock } = e.currentTarget.dataset;
    
    this.setData({
      showHealthRecord: true,
      currentFlock: flock
    });
  },

  /**
   * 隐藏弹窗
   */
  hideModal() {
    this.setData({
      showForm: false,
      showStatistics: false,
      showHealthRecord: false,
      isEditing: false,
      currentFlock: null
    });
  },

  /**
   * 表单输入处理
   */
  onFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 数字输入处理
   */
  onNumberInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: parseFloat(value) || 0
    });
  },

  /**
   * 选择器变更处理
   */
  onPickerChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 日期选择处理
   */
  onDateChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 保存鹅群信息
   */
  async saveFlock() {
    try {
      // 表单验证
      if (!this.validateForm()) {
        return;
      }
      
      this.setData({ loading: true });
      
      const operation = this.data.isEditing ? 'update' : 'create';
      const flockData = { ...this.data.formData };
      
      if (this.data.isEditing) {
        flockData.id = this.data.currentFlock._id;
      }
      
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageFlocks',
          data: {
            operation,
            flockData
          }
        }
      });
      
      if (result.result.success) {
        wx.showToast({
          title: this.data.isEditing ? '更新成功' : '创建成功',
          icon: 'success'
        });
        
        this.hideModal();
        await this.loadFlockData();
      } else {
        wx.showToast({
          title: result.result.error || '保存失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('保存鹅群失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 删除鹅群
   */
  async deleteFlock(e) {
    const { flock } = e.currentTarget.dataset;
    
    // 检查删除权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.FLOCK_DELETE)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    try {
      const result = await wx.showModal({
        title: '确认删除',
        content: `确定要删除鹅群"${flock.name}"吗？删除后无法恢复。`
      });
      
      if (!result.confirm) return;
      
      this.setData({ loading: true });
      
      const deleteResult = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageFlocks',
          data: {
            operation: 'delete',
            flockData: { id: flock._id }
          }
        }
      });
      
      if (deleteResult.result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        await this.loadFlockData();
      } else {
        wx.showToast({
          title: deleteResult.result.error || '删除失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('删除鹅群失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data;
    
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入鹅群名称',
        icon: 'error'
      });
      return false;
    }
    
    if (!formData.code.trim()) {
      wx.showToast({
        title: '请输入鹅群编号',
        icon: 'error'
      });
      return false;
    }
    
    if (formData.count <= 0) {
      wx.showToast({
        title: '请输入有效的鹅群数量',
        icon: 'error'
      });
      return false;
    }
    
    return true;
  },

  /**
   * 生成鹅群编号
   */
  generateFlockCode() {
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2);
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    return `FL${year}${month}${day}${random}`;
  },

  /**
   * 获取当前日期
   */
  getCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 获取品种信息
   */
  getBreedInfo(breed) {
    return this.data.breedOptions.find(option => option.value === breed) || this.data.breedOptions[0];
  },

  /**
   * 获取状态信息
   */
  getStatusInfo(status) {
    return this.data.statusOptions.find(option => option.value === status) || this.data.statusOptions[0];
  },

  /**
   * 获取健康状态信息
   */
  getHealthStatusInfo(status) {
    return this.data.healthStatusOptions.find(option => option.value === status) || this.data.healthStatusOptions[0];
  },

  /**
   * 筛选数据
   */
  onFilterChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`filters.${field}`]: value
    });
    
    // 重新加载数据
    this.loadFlockData();
  },

  /**
   * 搜索鹅群
   */
  onSearchInput(e) {
    const { value } = e.detail;
    
    this.setData({
      'filters.keyword': value
    });
    
    // 延迟搜索
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.loadFlockData();
    }, 500);
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    await this.loadFlockData();
    wx.stopPullDownRefresh();
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '鹅群管理',
      path: '/pages/admin/flock/flock'
    };
  }
});
