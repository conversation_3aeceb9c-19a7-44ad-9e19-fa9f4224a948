/**
 * 数据管理页面
 * Data Management Page
 * 
 * 基于通用数据管理权限：
 * - DATA_EXPORT: 数据导出
 * - DATA_IMPORT: 数据导入
 * - DATA_ANALYTICS: 数据分析
 */

const { getCurrentUserPermissions, PERMISSIONS, hasPermission } = require('../../../utils/role-permission');

Page({
  data: {
    loading: false,
    userPermissions: null,
    
    // 当前活动标签
    activeTab: 'export', // export, import, analytics
    
    // 导出配置
    exportConfig: {
      dataType: 'flocks', // flocks, finance, users, materials, health
      format: 'excel', // excel, csv, json
      dateRange: 'all', // all, today, week, month, quarter, year, custom
      startDate: '',
      endDate: '',
      fields: [],
      filters: {}
    },
    
    // 导入配置
    importConfig: {
      dataType: 'flocks',
      file: null,
      fileName: '',
      mapping: {},
      preview: [],
      errors: []
    },
    
    // 分析配置
    analyticsConfig: {
      type: 'overview', // overview, trend, comparison, distribution
      dataSource: 'flocks',
      timeRange: 'month',
      metrics: [],
      dimensions: []
    },
    
    // 数据类型选项
    dataTypeOptions: [
      { 
        value: 'flocks', 
        label: '鹅群数据', 
        description: '鹅群基本信息、数量、状态等',
        icon: '🦢',
        fields: ['name', 'code', 'breed', 'count', 'age_weeks', 'status', 'location', 'manager']
      },
      { 
        value: 'finance', 
        label: '财务数据', 
        description: '收支记录、预算、成本分析等',
        icon: '💰',
        fields: ['type', 'category', 'amount', 'description', 'date', 'status', 'payment_method']
      },
      { 
        value: 'users', 
        label: '用户数据', 
        description: '用户信息、角色、权限等',
        icon: '👥',
        fields: ['name', 'phone', 'email', 'role', 'department', 'status', 'last_login']
      },
      { 
        value: 'materials', 
        label: '物料数据', 
        description: '物料库存、采购、消耗等',
        icon: '📦',
        fields: ['name', 'category', 'quantity', 'unit', 'price', 'supplier', 'status']
      },
      { 
        value: 'health', 
        label: '健康数据', 
        description: '健康记录、疫苗、诊断等',
        icon: '🏥',
        fields: ['flock_id', 'date', 'type', 'description', 'diagnosis', 'treatment', 'status']
      }
    ],
    
    // 导出格式选项
    formatOptions: [
      { value: 'excel', label: 'Excel文件 (.xlsx)', icon: '📊' },
      { value: 'csv', label: 'CSV文件 (.csv)', icon: '📄' },
      { value: 'json', label: 'JSON文件 (.json)', icon: '🔧' }
    ],
    
    // 时间范围选项
    dateRangeOptions: [
      { value: 'all', label: '全部数据' },
      { value: 'today', label: '今天' },
      { value: 'week', label: '本周' },
      { value: 'month', label: '本月' },
      { value: 'quarter', label: '本季度' },
      { value: 'year', label: '本年' },
      { value: 'custom', label: '自定义范围' }
    ],
    
    // 分析类型选项
    analyticsTypeOptions: [
      { 
        value: 'overview', 
        label: '数据概览', 
        description: '总体数据统计和趋势',
        icon: '📈'
      },
      { 
        value: 'trend', 
        label: '趋势分析', 
        description: '时间序列数据变化趋势',
        icon: '📊'
      },
      { 
        value: 'comparison', 
        label: '对比分析', 
        description: '不同维度数据对比',
        icon: '⚖️'
      },
      { 
        value: 'distribution', 
        label: '分布分析', 
        description: '数据分布和占比情况',
        icon: '🥧'
      }
    ],
    
    // 导出历史
    exportHistory: [],
    
    // 导入历史
    importHistory: [],
    
    // 分析结果
    analyticsResults: null,
    
    // 页面状态
    showFieldSelector: false,
    showPreview: false,
    showResults: false
  },

  async onLoad(options) {
    await this.checkPermissions();
    await this.loadHistory();
    
    // 处理页面参数
    if (options.action) {
      this.setData({ activeTab: options.action });
    }
  },

  /**
   * 检查用户权限
   */
  async checkPermissions() {
    try {
      const userPermissions = await getCurrentUserPermissions();
      
      // 检查是否有数据管理权限
      const hasDataPermission = hasPermission(userPermissions.role, PERMISSIONS.DATA_EXPORT) ||
                               hasPermission(userPermissions.role, PERMISSIONS.DATA_IMPORT) ||
                               hasPermission(userPermissions.role, PERMISSIONS.DATA_ANALYTICS);
      
      if (!hasDataPermission) {
        wx.showModal({
          title: '权限不足',
          content: '您没有访问数据管理的权限',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }
      
      this.setData({
        userPermissions
      });
      
    } catch (error) {
      console.error('权限检查失败:', error);
      wx.showToast({
        title: '权限检查失败',
        icon: 'error'
      });
    }
  },

  /**
   * 加载历史记录
   */
  async loadHistory() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageData',
          data: {
            operation: 'getHistory'
          }
        }
      });
      
      if (result.result.success) {
        this.setData({
          exportHistory: result.result.data.exports || [],
          importHistory: result.result.data.imports || []
        });
      }
      
    } catch (error) {
      console.error('加载历史记录失败:', error);
    }
  },

  /**
   * 切换标签页
   */
  switchTab(e) {
    const { tab } = e.currentTarget.dataset;
    this.setData({ activeTab: tab });
  },

  // ========== 数据导出功能 ==========

  /**
   * 数据类型选择
   */
  onDataTypeChange(e) {
    const { value } = e.detail;
    const selectedType = this.data.dataTypeOptions.find(option => option.value === value);
    
    this.setData({
      'exportConfig.dataType': value,
      'exportConfig.fields': selectedType ? selectedType.fields : []
    });
  },

  /**
   * 导出格式选择
   */
  onFormatChange(e) {
    const { value } = e.detail;
    this.setData({
      'exportConfig.format': value
    });
  },

  /**
   * 时间范围选择
   */
  onDateRangeChange(e) {
    const { value } = e.detail;
    this.setData({
      'exportConfig.dateRange': value
    });
    
    // 如果选择自定义范围，显示日期选择器
    if (value === 'custom') {
      this.setData({
        'exportConfig.startDate': this.getCurrentDate(),
        'exportConfig.endDate': this.getCurrentDate()
      });
    }
  },

  /**
   * 日期选择
   */
  onDateChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`exportConfig.${field}`]: value
    });
  },

  /**
   * 显示字段选择器
   */
  showFieldSelector() {
    this.setData({
      showFieldSelector: true
    });
  },

  /**
   * 字段选择切换
   */
  toggleField(e) {
    const { field } = e.currentTarget.dataset;
    const fields = [...this.data.exportConfig.fields];
    const index = fields.indexOf(field);
    
    if (index > -1) {
      fields.splice(index, 1);
    } else {
      fields.push(field);
    }
    
    this.setData({
      'exportConfig.fields': fields
    });
  },

  /**
   * 执行数据导出
   */
  async executeExport() {
    // 检查导出权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.DATA_EXPORT)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    try {
      // 验证导出配置
      if (!this.validateExportConfig()) {
        return;
      }
      
      wx.showLoading({ title: '正在导出数据...' });
      
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageData',
          data: {
            operation: 'export',
            config: this.data.exportConfig
          }
        }
      });
      
      if (result.result.success) {
        // 下载文件
        wx.downloadFile({
          url: result.result.data.download_url,
          success: (res) => {
            wx.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                wx.showToast({
                  title: '导出成功',
                  icon: 'success'
                });
                
                // 刷新历史记录
                this.loadHistory();
              }
            });
          },
          fail: () => {
            wx.showToast({
              title: '下载失败',
              icon: 'error'
            });
          }
        });
      } else {
        wx.showToast({
          title: result.result.error || '导出失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('导出失败:', error);
      wx.showToast({
        title: '导出失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 验证导出配置
   */
  validateExportConfig() {
    const { exportConfig } = this.data;
    
    if (!exportConfig.dataType) {
      wx.showToast({
        title: '请选择数据类型',
        icon: 'error'
      });
      return false;
    }
    
    if (exportConfig.fields.length === 0) {
      wx.showToast({
        title: '请选择导出字段',
        icon: 'error'
      });
      return false;
    }
    
    if (exportConfig.dateRange === 'custom') {
      if (!exportConfig.startDate || !exportConfig.endDate) {
        wx.showToast({
          title: '请选择日期范围',
          icon: 'error'
        });
        return false;
      }
      
      if (exportConfig.startDate > exportConfig.endDate) {
        wx.showToast({
          title: '开始日期不能晚于结束日期',
          icon: 'error'
        });
        return false;
      }
    }
    
    return true;
  },

  // ========== 数据导入功能 ==========

  /**
   * 选择导入文件
   */
  chooseImportFile() {
    // 检查导入权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.DATA_IMPORT)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      extension: ['xlsx', 'xls', 'csv'],
      success: (res) => {
        const file = res.tempFiles[0];
        this.setData({
          'importConfig.file': file,
          'importConfig.fileName': file.name
        });
        
        // 预览文件内容
        this.previewImportFile(file);
      },
      fail: (error) => {
        console.error('选择文件失败:', error);
        wx.showToast({
          title: '选择文件失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 预览导入文件
   */
  async previewImportFile(file) {
    try {
      wx.showLoading({ title: '正在解析文件...' });
      
      // 上传文件到云存储
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath: `imports/${Date.now()}_${file.name}`,
        filePath: file.path
      });
      
      // 解析文件内容
      const parseResult = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageData',
          data: {
            operation: 'parseImportFile',
            fileId: uploadResult.fileID,
            dataType: this.data.importConfig.dataType
          }
        }
      });
      
      if (parseResult.result.success) {
        this.setData({
          'importConfig.preview': parseResult.result.data.preview,
          'importConfig.mapping': parseResult.result.data.mapping,
          'importConfig.errors': parseResult.result.data.errors || [],
          showPreview: true
        });
      } else {
        wx.showToast({
          title: parseResult.result.error || '文件解析失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('预览文件失败:', error);
      wx.showToast({
        title: '预览文件失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 执行数据导入
   */
  async executeImport() {
    try {
      // 验证导入配置
      if (!this.validateImportConfig()) {
        return;
      }
      
      wx.showLoading({ title: '正在导入数据...' });
      
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageData',
          data: {
            operation: 'import',
            config: this.data.importConfig
          }
        }
      });
      
      if (result.result.success) {
        wx.showToast({
          title: `导入成功，共${result.result.data.imported_count}条记录`,
          icon: 'success'
        });
        
        // 重置导入配置
        this.setData({
          'importConfig.file': null,
          'importConfig.fileName': '',
          'importConfig.preview': [],
          'importConfig.errors': [],
          showPreview: false
        });
        
        // 刷新历史记录
        this.loadHistory();
      } else {
        wx.showToast({
          title: result.result.error || '导入失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('导入失败:', error);
      wx.showToast({
        title: '导入失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 验证导入配置
   */
  validateImportConfig() {
    const { importConfig } = this.data;
    
    if (!importConfig.file) {
      wx.showToast({
        title: '请选择导入文件',
        icon: 'error'
      });
      return false;
    }
    
    if (importConfig.errors.length > 0) {
      wx.showToast({
        title: '请先修复数据错误',
        icon: 'error'
      });
      return false;
    }
    
    return true;
  },

  // ========== 数据分析功能 ==========

  /**
   * 分析类型选择
   */
  onAnalyticsTypeChange(e) {
    const { value } = e.detail;
    this.setData({
      'analyticsConfig.type': value
    });
  },

  /**
   * 数据源选择
   */
  onDataSourceChange(e) {
    const { value } = e.detail;
    this.setData({
      'analyticsConfig.dataSource': value
    });
  },

  /**
   * 时间范围选择
   */
  onTimeRangeChange(e) {
    const { value } = e.detail;
    this.setData({
      'analyticsConfig.timeRange': value
    });
  },

  /**
   * 执行数据分析
   */
  async executeAnalytics() {
    // 检查分析权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.DATA_ANALYTICS)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    try {
      wx.showLoading({ title: '正在分析数据...' });
      
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageData',
          data: {
            operation: 'analytics',
            config: this.data.analyticsConfig
          }
        }
      });
      
      if (result.result.success) {
        this.setData({
          analyticsResults: result.result.data,
          showResults: true
        });
        
        wx.showToast({
          title: '分析完成',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: result.result.error || '分析失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('分析失败:', error);
      wx.showToast({
        title: '分析失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // ========== 工具方法 ==========

  /**
   * 获取当前日期
   */
  getCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 获取数据类型信息
   */
  getDataTypeInfo(type) {
    return this.data.dataTypeOptions.find(option => option.value === type) || this.data.dataTypeOptions[0];
  },

  /**
   * 隐藏弹窗
   */
  hideModal() {
    this.setData({
      showFieldSelector: false,
      showPreview: false,
      showResults: false
    });
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    await this.loadHistory();
    wx.stopPullDownRefresh();
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '数据管理',
      path: '/pages/admin/data/data'
    };
  }
});
