/**
 * 财务管理页面
 * Finance Management Page
 * 
 * 基于租户级财务管理权限：
 * - FINANCE_VIEW_ALL: 查看所有财务数据
 * - FINANCE_VIEW_OWN: 查看自己的财务数据
 * - FINANCE_CREATE: 创建财务申请
 * - FINANCE_EDIT: 编辑财务申请
 * - FINANCE_DELETE: 删除财务申请
 * - FINANCE_APPROVE: 审批财务申请
 * - FINANCE_EXPORT: 导出财务数据
 * - FINANCE_REPORTS: 查看财务报表
 * - FINANCE_BUDGET: 预算管理
 * - FINANCE_COST_ANALYSIS: 成本分析
 * - FINANCE_PROFIT_ANALYSIS: 利润核算
 */

const { getCurrentUserPermissions, PERMISSIONS, hasPermission } = require('../../../utils/role-permission');
const { FinancePermissionChecker } = require('../../../utils/role-permission');

Page({
  data: {
    loading: false,
    userPermissions: null,
    financePermissions: null,
    
    // 财务记录列表
    financeList: [],
    
    // 当前编辑的财务记录
    currentFinance: null,
    
    // 表单数据
    formData: {
      type: 'expense', // expense, income, investment, loan
      category: '',
      amount: 0,
      description: '',
      date: '',
      payment_method: 'cash', // cash, bank_transfer, alipay, wechat
      invoice_number: '',
      supplier: '',
      related_flock: '',
      status: 'pending', // pending, approved, rejected, paid
      attachments: [],
      notes: ''
    },
    
    // 筛选条件
    filters: {
      type: '',
      category: '',
      status: '',
      date_range: '',
      amount_range: '',
      keyword: ''
    },
    
    // 页面状态
    showForm: false,
    showReports: false,
    showBudget: false,
    showAnalysis: false,
    isEditing: false,
    activeTab: 'list', // list, reports, budget, analysis
    
    // 统计数据
    statistics: {
      total_income: 0,
      total_expense: 0,
      net_profit: 0,
      pending_amount: 0,
      approved_amount: 0,
      monthly_budget: 0,
      budget_used: 0,
      budget_remaining: 0
    },
    
    // 财务类型选项
    typeOptions: [
      { value: 'expense', label: '支出', color: '#f5222d', icon: '💸' },
      { value: 'income', label: '收入', color: '#52c41a', icon: '💰' },
      { value: 'investment', label: '投资', color: '#1890ff', icon: '📈' },
      { value: 'loan', label: '借贷', color: '#faad14', icon: '🏦' }
    ],
    
    // 支出类别选项
    expenseCategories: [
      { value: 'feed', label: '饲料费用' },
      { value: 'medicine', label: '医疗费用' },
      { value: 'equipment', label: '设备采购' },
      { value: 'labor', label: '人工费用' },
      { value: 'utilities', label: '水电费用' },
      { value: 'transport', label: '运输费用' },
      { value: 'maintenance', label: '维护费用' },
      { value: 'insurance', label: '保险费用' },
      { value: 'other', label: '其他支出' }
    ],
    
    // 收入类别选项
    incomeCategories: [
      { value: 'sales', label: '销售收入' },
      { value: 'subsidy', label: '政府补贴' },
      { value: 'investment_return', label: '投资收益' },
      { value: 'service', label: '服务收入' },
      { value: 'other', label: '其他收入' }
    ],
    
    // 支付方式选项
    paymentMethods: [
      { value: 'cash', label: '现金', icon: '💵' },
      { value: 'bank_transfer', label: '银行转账', icon: '🏦' },
      { value: 'alipay', label: '支付宝', icon: '📱' },
      { value: 'wechat', label: '微信支付', icon: '💬' },
      { value: 'check', label: '支票', icon: '📝' },
      { value: 'credit_card', label: '信用卡', icon: '💳' }
    ],
    
    // 状态选项
    statusOptions: [
      { value: 'pending', label: '待审批', color: '#faad14' },
      { value: 'approved', label: '已审批', color: '#52c41a' },
      { value: 'rejected', label: '已拒绝', color: '#f5222d' },
      { value: 'paid', label: '已支付', color: '#1890ff' },
      { value: 'cancelled', label: '已取消', color: '#8c8c8c' }
    ],
    
    // 报表类型选项
    reportTypes: [
      { value: 'monthly', label: '月度报表', description: '按月统计收支情况' },
      { value: 'quarterly', label: '季度报表', description: '按季度分析财务状况' },
      { value: 'yearly', label: '年度报表', description: '年度财务总结报告' },
      { value: 'category', label: '分类报表', description: '按类别统计分析' },
      { value: 'profit_loss', label: '损益报表', description: '利润损失分析' }
    ]
  },

  async onLoad(options) {
    await this.checkPermissions();
    await this.loadFinanceData();
    
    // 处理页面参数
    if (options.action === 'create') {
      this.showCreateForm();
    } else if (options.tab) {
      this.setData({ activeTab: options.tab });
    }
  },

  /**
   * 检查用户权限
   */
  async checkPermissions() {
    try {
      const userPermissions = await getCurrentUserPermissions();
      
      // 获取财务权限详情
      const financePermissions = FinancePermissionChecker.getFinancePermissions(userPermissions.role);
      
      // 检查是否有财务查看权限
      if (!financePermissions.canViewAll && !financePermissions.canViewOwn) {
        wx.showModal({
          title: '权限不足',
          content: '您没有访问财务管理的权限',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }
      
      this.setData({
        userPermissions,
        financePermissions
      });
      
    } catch (error) {
      console.error('权限检查失败:', error);
      wx.showToast({
        title: '权限检查失败',
        icon: 'error'
      });
    }
  },

  /**
   * 加载财务数据
   */
  async loadFinanceData() {
    try {
      this.setData({ loading: true });
      
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageFinance',
          data: {
            operation: 'list',
            filters: this.data.filters,
            page: 1,
            limit: 50
          }
        }
      });
      
      if (result.result.success) {
        this.setData({
          financeList: result.result.data.list,
          statistics: result.result.data.statistics
        });
      } else {
        wx.showToast({
          title: result.result.error || '加载失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('加载财务数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 显示创建表单
   */
  showCreateForm() {
    // 检查创建权限
    if (!this.data.financePermissions.canCreate) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showForm: true,
      isEditing: false,
      currentFinance: null,
      formData: {
        type: 'expense',
        category: '',
        amount: 0,
        description: '',
        date: this.getCurrentDate(),
        payment_method: 'cash',
        invoice_number: '',
        supplier: '',
        related_flock: '',
        status: 'pending',
        attachments: [],
        notes: ''
      }
    });
  },

  /**
   * 显示编辑表单
   */
  showEditForm(e) {
    const { finance } = e.currentTarget.dataset;
    
    // 检查编辑权限
    const canEdit = this.data.financePermissions.canEdit || 
                   (this.data.financePermissions.canViewOwn && finance.creator_id === this.data.userPermissions.userId);
    
    if (!canEdit) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showForm: true,
      isEditing: true,
      currentFinance: finance,
      formData: {
        type: finance.type,
        category: finance.category,
        amount: finance.amount,
        description: finance.description,
        date: finance.date,
        payment_method: finance.payment_method,
        invoice_number: finance.invoice_number || '',
        supplier: finance.supplier || '',
        related_flock: finance.related_flock || '',
        status: finance.status,
        attachments: finance.attachments || [],
        notes: finance.notes || ''
      }
    });
  },

  /**
   * 审批财务申请
   */
  async approveFinance(e) {
    const { finance, action } = e.currentTarget.dataset; // action: approve, reject
    
    // 检查审批权限
    if (!this.data.financePermissions.canApprove) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    try {
      const result = await wx.showModal({
        title: action === 'approve' ? '确认审批' : '确认拒绝',
        content: `确定要${action === 'approve' ? '审批通过' : '拒绝'}这条财务申请吗？`
      });
      
      if (!result.confirm) return;
      
      this.setData({ loading: true });
      
      const approveResult = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageFinance',
          data: {
            operation: 'approve',
            financeData: { 
              id: finance._id, 
              action: action,
              status: action === 'approve' ? 'approved' : 'rejected'
            }
          }
        }
      });
      
      if (approveResult.result.success) {
        wx.showToast({
          title: action === 'approve' ? '审批成功' : '拒绝成功',
          icon: 'success'
        });
        
        await this.loadFinanceData();
      } else {
        wx.showToast({
          title: approveResult.result.error || '操作失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('审批操作失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 导出财务数据
   */
  async exportFinanceData() {
    // 检查导出权限
    if (!this.data.financePermissions.canExport) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    try {
      wx.showLoading({ title: '正在导出...' });
      
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageFinance',
          data: {
            operation: 'export',
            filters: this.data.filters,
            format: 'excel'
          }
        }
      });
      
      if (result.result.success) {
        // 下载文件
        wx.downloadFile({
          url: result.result.data.download_url,
          success: (res) => {
            wx.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                wx.showToast({
                  title: '导出成功',
                  icon: 'success'
                });
              }
            });
          }
        });
      } else {
        wx.showToast({
          title: result.result.error || '导出失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('导出失败:', error);
      wx.showToast({
        title: '导出失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 显示财务报表
   */
  showFinanceReports() {
    // 检查报表权限
    if (!this.data.financePermissions.canViewReports) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showReports: true
    });
  },

  /**
   * 显示预算管理
   */
  showBudgetManagement() {
    // 检查预算权限
    if (!this.data.financePermissions.canManageBudget) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showBudget: true
    });
  },

  /**
   * 显示成本分析
   */
  showCostAnalysis() {
    this.setData({
      showAnalysis: true
    });
  },

  /**
   * 切换标签页
   */
  switchTab(e) {
    const { tab } = e.currentTarget.dataset;
    this.setData({ activeTab: tab });
    
    // 根据标签页加载对应数据
    switch (tab) {
      case 'reports':
        this.showFinanceReports();
        break;
      case 'budget':
        this.showBudgetManagement();
        break;
      case 'analysis':
        this.showCostAnalysis();
        break;
    }
  },

  /**
   * 隐藏弹窗
   */
  hideModal() {
    this.setData({
      showForm: false,
      showReports: false,
      showBudget: false,
      showAnalysis: false,
      isEditing: false,
      currentFinance: null
    });
  },

  /**
   * 表单输入处理
   */
  onFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
    
    // 根据类型更新类别选项
    if (field === 'type') {
      this.setData({
        'formData.category': ''
      });
    }
  },

  /**
   * 数字输入处理
   */
  onNumberInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: parseFloat(value) || 0
    });
  },

  /**
   * 选择器变更处理
   */
  onPickerChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 日期选择处理
   */
  onDateChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 保存财务记录
   */
  async saveFinance() {
    try {
      // 表单验证
      if (!this.validateForm()) {
        return;
      }
      
      this.setData({ loading: true });
      
      const operation = this.data.isEditing ? 'update' : 'create';
      const financeData = { ...this.data.formData };
      
      if (this.data.isEditing) {
        financeData.id = this.data.currentFinance._id;
      }
      
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageFinance',
          data: {
            operation,
            financeData
          }
        }
      });
      
      if (result.result.success) {
        wx.showToast({
          title: this.data.isEditing ? '更新成功' : '创建成功',
          icon: 'success'
        });
        
        this.hideModal();
        await this.loadFinanceData();
      } else {
        wx.showToast({
          title: result.result.error || '保存失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('保存财务记录失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data;
    
    if (!formData.description.trim()) {
      wx.showToast({
        title: '请输入财务描述',
        icon: 'error'
      });
      return false;
    }
    
    if (formData.amount <= 0) {
      wx.showToast({
        title: '请输入有效金额',
        icon: 'error'
      });
      return false;
    }
    
    if (!formData.date) {
      wx.showToast({
        title: '请选择日期',
        icon: 'error'
      });
      return false;
    }
    
    return true;
  },

  /**
   * 获取当前日期
   */
  getCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 获取类型信息
   */
  getTypeInfo(type) {
    return this.data.typeOptions.find(option => option.value === type) || this.data.typeOptions[0];
  },

  /**
   * 获取状态信息
   */
  getStatusInfo(status) {
    return this.data.statusOptions.find(option => option.value === status) || this.data.statusOptions[0];
  },

  /**
   * 获取类别选项
   */
  getCategoryOptions(type) {
    return type === 'expense' ? this.data.expenseCategories : this.data.incomeCategories;
  },

  /**
   * 筛选数据
   */
  onFilterChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`filters.${field}`]: value
    });
    
    // 重新加载数据
    this.loadFinanceData();
  },

  /**
   * 搜索财务记录
   */
  onSearchInput(e) {
    const { value } = e.detail;
    
    this.setData({
      'filters.keyword': value
    });
    
    // 延迟搜索
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.loadFinanceData();
    }, 500);
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    await this.loadFinanceData();
    wx.stopPullDownRefresh();
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '财务管理',
      path: '/pages/admin/finance/finance'
    };
  }
});
