/**
 * 用户管理页面
 * User Management Page
 * 
 * 基于通用用户管理权限：
 * - USER_VIEW: 查看用户信息
 * - USER_CREATE: 创建用户
 * - USER_EDIT: 编辑用户
 * - USER_DELETE: 删除用户
 * - USER_ROLE_ASSIGN: 分配角色
 */

const { 
  getCurrentUserPermissions, 
  PERMISSIONS, 
  ROLES,
  ROLE_PERMISSIONS,
  hasPermission,
  getRoleName,
  compareRoles
} = require('../../../utils/role-permission');

Page({
  data: {
    loading: false,
    userPermissions: null,
    
    // 用户列表数据
    userList: [],
    
    // 当前编辑的用户
    currentUser: null,
    
    // 表单数据
    formData: {
      name: '',
      phone: '',
      email: '',
      role: 'user',
      department: '',
      position: '',
      status: 'active', // active, inactive, suspended
      avatar: '',
      description: '',
      permissions: []
    },
    
    // 筛选条件
    filters: {
      role: '',
      status: '',
      department: '',
      keyword: ''
    },
    
    // 页面状态
    showForm: false,
    showRoleConfig: false,
    showPermissionConfig: false,
    isEditing: false,
    
    // 统计数据
    statistics: {
      total_users: 0,
      active_users: 0,
      inactive_users: 0,
      admin_users: 0,
      last_login_count: 0
    },
    
    // 角色选项
    roleOptions: [],
    
    // 部门选项
    departmentOptions: [
      { value: 'management', label: '管理部门' },
      { value: 'production', label: '生产部门' },
      { value: 'finance', label: '财务部门' },
      { value: 'hr', label: '人事部门' },
      { value: 'technology', label: '技术部门' },
      { value: 'sales', label: '销售部门' },
      { value: 'other', label: '其他部门' }
    ],
    
    // 状态选项
    statusOptions: [
      { value: 'active', label: '正常', color: '#52c41a' },
      { value: 'inactive', label: '未激活', color: '#faad14' },
      { value: 'suspended', label: '已停用', color: '#f5222d' }
    ],
    
    // 权限分组
    permissionGroups: {
      platform: {
        name: '平台权限',
        permissions: []
      },
      tenant: {
        name: '租户权限',
        permissions: []
      },
      common: {
        name: '通用权限',
        permissions: []
      }
    }
  },

  async onLoad(options) {
    await this.checkPermissions();
    await this.initializeRoleOptions();
    await this.loadUserData();
    
    // 处理页面参数
    if (options.action === 'create') {
      this.showCreateForm();
    }
  },

  /**
   * 检查用户权限
   */
  async checkPermissions() {
    try {
      const userPermissions = await getCurrentUserPermissions();
      
      // 检查是否有用户查看权限
      if (!hasPermission(userPermissions.role, PERMISSIONS.USER_VIEW)) {
        wx.showModal({
          title: '权限不足',
          content: '您没有访问用户管理的权限',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }
      
      this.setData({
        userPermissions
      });
      
    } catch (error) {
      console.error('权限检查失败:', error);
      wx.showToast({
        title: '权限检查失败',
        icon: 'error'
      });
    }
  },

  /**
   * 初始化角色选项
   */
  async initializeRoleOptions() {
    const currentUserRole = this.data.userPermissions.role;
    const roleOptions = [];
    
    // 根据当前用户角色过滤可分配的角色
    Object.values(ROLES).forEach(role => {
      // 只能分配等级不高于自己的角色
      if (compareRoles(role, currentUserRole) <= 0) {
        roleOptions.push({
          value: role,
          label: getRoleName(role),
          permissionCount: ROLE_PERMISSIONS[role] ? ROLE_PERMISSIONS[role].length : 0
        });
      }
    });
    
    this.setData({
      roleOptions
    });
  },

  /**
   * 加载用户数据
   */
  async loadUserData() {
    try {
      this.setData({ loading: true });
      
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageUsers',
          data: {
            operation: 'list',
            filters: this.data.filters,
            page: 1,
            limit: 50
          }
        }
      });
      
      if (result.result.success) {
        this.setData({
          userList: result.result.data.list,
          statistics: result.result.data.statistics
        });
      } else {
        wx.showToast({
          title: result.result.error || '加载失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('加载用户数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 显示创建表单
   */
  showCreateForm() {
    // 检查创建权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.USER_CREATE)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showForm: true,
      isEditing: false,
      currentUser: null,
      formData: {
        name: '',
        phone: '',
        email: '',
        role: 'user',
        department: '',
        position: '',
        status: 'active',
        avatar: '',
        description: '',
        permissions: []
      }
    });
  },

  /**
   * 显示编辑表单
   */
  showEditForm(e) {
    const { user } = e.currentTarget.dataset;
    
    // 检查编辑权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.USER_EDIT)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    // 检查是否可以编辑该用户（不能编辑比自己权限高的用户）
    if (compareRoles(user.role, this.data.userPermissions.role) > 0) {
      wx.showToast({
        title: '无法编辑权限更高的用户',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showForm: true,
      isEditing: true,
      currentUser: user,
      formData: {
        name: user.name,
        phone: user.phone || '',
        email: user.email || '',
        role: user.role,
        department: user.department || '',
        position: user.position || '',
        status: user.status,
        avatar: user.avatar || '',
        description: user.description || '',
        permissions: user.custom_permissions || []
      }
    });
  },

  /**
   * 显示角色配置
   */
  showRoleConfiguration(e) {
    const { user } = e.currentTarget.dataset;
    
    // 检查角色分配权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.USER_ROLE_ASSIGN)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showRoleConfig: true,
      currentUser: user
    });
  },

  /**
   * 显示权限配置
   */
  showPermissionConfiguration(e) {
    const { user } = e.currentTarget.dataset;
    
    // 检查角色分配权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.USER_ROLE_ASSIGN)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showPermissionConfig: true,
      currentUser: user
    });
  },

  /**
   * 删除用户
   */
  async deleteUser(e) {
    const { user } = e.currentTarget.dataset;
    
    // 检查删除权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.USER_DELETE)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    // 检查是否可以删除该用户
    if (compareRoles(user.role, this.data.userPermissions.role) > 0) {
      wx.showToast({
        title: '无法删除权限更高的用户',
        icon: 'error'
      });
      return;
    }
    
    // 不能删除自己
    if (user._id === this.data.userPermissions.userId) {
      wx.showToast({
        title: '不能删除自己',
        icon: 'error'
      });
      return;
    }
    
    try {
      const result = await wx.showModal({
        title: '确认删除',
        content: `确定要删除用户"${user.name}"吗？删除后无法恢复。`
      });
      
      if (!result.confirm) return;
      
      this.setData({ loading: true });
      
      const deleteResult = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageUsers',
          data: {
            operation: 'delete',
            userData: { id: user._id }
          }
        }
      });
      
      if (deleteResult.result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        await this.loadUserData();
      } else {
        wx.showToast({
          title: deleteResult.result.error || '删除失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('删除用户失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 重置用户密码
   */
  async resetUserPassword(e) {
    const { user } = e.currentTarget.dataset;
    
    // 检查编辑权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.USER_EDIT)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    try {
      const result = await wx.showModal({
        title: '确认重置密码',
        content: `确定要重置用户"${user.name}"的密码吗？新密码将发送到用户手机。`
      });
      
      if (!result.confirm) return;
      
      this.setData({ loading: true });
      
      const resetResult = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageUsers',
          data: {
            operation: 'resetPassword',
            userData: { id: user._id }
          }
        }
      });
      
      if (resetResult.result.success) {
        wx.showToast({
          title: '密码重置成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: resetResult.result.error || '重置失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('重置密码失败:', error);
      wx.showToast({
        title: '重置失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 切换用户状态
   */
  async toggleUserStatus(e) {
    const { user } = e.currentTarget.dataset;
    
    // 检查编辑权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.USER_EDIT)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    const newStatus = user.status === 'active' ? 'suspended' : 'active';
    const actionText = newStatus === 'active' ? '启用' : '停用';
    
    try {
      const result = await wx.showModal({
        title: `确认${actionText}`,
        content: `确定要${actionText}用户"${user.name}"吗？`
      });
      
      if (!result.confirm) return;
      
      this.setData({ loading: true });
      
      const updateResult = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageUsers',
          data: {
            operation: 'updateStatus',
            userData: { 
              id: user._id,
              status: newStatus
            }
          }
        }
      });
      
      if (updateResult.result.success) {
        wx.showToast({
          title: `${actionText}成功`,
          icon: 'success'
        });
        
        await this.loadUserData();
      } else {
        wx.showToast({
          title: updateResult.result.error || `${actionText}失败`,
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error(`${actionText}用户失败:`, error);
      wx.showToast({
        title: `${actionText}失败`,
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 隐藏弹窗
   */
  hideModal() {
    this.setData({
      showForm: false,
      showRoleConfig: false,
      showPermissionConfig: false,
      isEditing: false,
      currentUser: null
    });
  },

  /**
   * 表单输入处理
   */
  onFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 选择器变更处理
   */
  onPickerChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 角色变更处理
   */
  onRoleChange(e) {
    const { value } = e.detail;
    const selectedRole = this.data.roleOptions[value];
    
    this.setData({
      'formData.role': selectedRole.value,
      'formData.permissions': [] // 重置自定义权限
    });
  },

  /**
   * 保存用户信息
   */
  async saveUser() {
    try {
      // 表单验证
      if (!this.validateForm()) {
        return;
      }
      
      this.setData({ loading: true });
      
      const operation = this.data.isEditing ? 'update' : 'create';
      const userData = { ...this.data.formData };
      
      if (this.data.isEditing) {
        userData.id = this.data.currentUser._id;
      }
      
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageUsers',
          data: {
            operation,
            userData
          }
        }
      });
      
      if (result.result.success) {
        wx.showToast({
          title: this.data.isEditing ? '更新成功' : '创建成功',
          icon: 'success'
        });
        
        this.hideModal();
        await this.loadUserData();
      } else {
        wx.showToast({
          title: result.result.error || '保存失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('保存用户失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data;
    
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入用户姓名',
        icon: 'error'
      });
      return false;
    }
    
    if (formData.phone && !this.isValidPhone(formData.phone)) {
      wx.showToast({
        title: '请输入有效的手机号',
        icon: 'error'
      });
      return false;
    }
    
    if (formData.email && !this.isValidEmail(formData.email)) {
      wx.showToast({
        title: '请输入有效的邮箱地址',
        icon: 'error'
      });
      return false;
    }
    
    return true;
  },

  /**
   * 验证手机号格式
   */
  isValidPhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  /**
   * 验证邮箱格式
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * 获取角色信息
   */
  getRoleInfo(role) {
    return this.data.roleOptions.find(option => option.value === role) || 
           { value: role, label: getRoleName(role), permissionCount: 0 };
  },

  /**
   * 获取状态信息
   */
  getStatusInfo(status) {
    return this.data.statusOptions.find(option => option.value === status) || this.data.statusOptions[0];
  },

  /**
   * 筛选数据
   */
  onFilterChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`filters.${field}`]: value
    });
    
    // 重新加载数据
    this.loadUserData();
  },

  /**
   * 搜索用户
   */
  onSearchInput(e) {
    const { value } = e.detail;
    
    this.setData({
      'filters.keyword': value
    });
    
    // 延迟搜索
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.loadUserData();
    }, 500);
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    await this.loadUserData();
    wx.stopPullDownRefresh();
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '用户管理',
      path: '/pages/admin/user/user'
    };
  }
});
