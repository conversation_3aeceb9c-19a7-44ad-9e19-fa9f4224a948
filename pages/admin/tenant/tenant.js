/**
 * 租户管理页面
 * Tenant Management Page
 * 
 * 基于平台级租户管理权限：
 * - PLATFORM_TENANT_VIEW: 查看租户信息
 * - PLATFORM_TENANT_CREATE: 创建租户
 * - PLATFORM_TENANT_EDIT: 编辑租户信息
 * - PLATFORM_TENANT_DELETE: 删除租户
 * - PLATFORM_TENANT_CONFIG: 配置租户权限
 * - PLATFORM_TENANT_SUBSCRIPTION: 管理租户订阅
 */

const { getCurrentUserPermissions, PERMISSIONS, hasPermission } = require('../../../utils/role-permission');
const { AdminPermissionManager } = require('../../../utils/admin-permission-manager');

Page({
  data: {
    loading: false,
    userPermissions: null,
    
    // 租户列表数据
    tenantList: [],
    
    // 当前编辑的租户
    currentTenant: null,
    
    // 表单数据
    formData: {
      name: '',
      code: '',
      contact_person: '',
      contact_phone: '',
      contact_email: '',
      address: '',
      industry: '',
      scale: 'small', // small, medium, large, enterprise
      subscription_plan: 'basic', // basic, standard, premium, enterprise
      max_users: 10,
      max_flocks: 50,
      status: 'active', // active, inactive, suspended
      description: '',
      features: []
    },
    
    // 筛选条件
    filters: {
      status: '',
      subscription_plan: '',
      scale: '',
      keyword: ''
    },
    
    // 页面状态
    showForm: false,
    showConfig: false,
    showSubscription: false,
    isEditing: false,
    
    // 统计数据
    statistics: {
      total_tenants: 0,
      active_tenants: 0,
      inactive_tenants: 0,
      total_users: 0,
      total_revenue: 0
    },
    
    // 行业选项
    industryOptions: [
      { value: 'agriculture', label: '农业养殖' },
      { value: 'food', label: '食品加工' },
      { value: 'trading', label: '贸易销售' },
      { value: 'logistics', label: '物流运输' },
      { value: 'technology', label: '科技服务' },
      { value: 'other', label: '其他' }
    ],
    
    // 规模选项
    scaleOptions: [
      { value: 'small', label: '小型（1-50人）' },
      { value: 'medium', label: '中型（51-200人）' },
      { value: 'large', label: '大型（201-1000人）' },
      { value: 'enterprise', label: '企业级（1000+人）' }
    ],
    
    // 订阅计划选项
    subscriptionOptions: [
      { 
        value: 'basic', 
        label: '基础版', 
        price: 99, 
        features: ['基础功能', '10个用户', '50个鹅群'],
        color: '#52c41a'
      },
      { 
        value: 'standard', 
        label: '标准版', 
        price: 299, 
        features: ['标准功能', '50个用户', '200个鹅群', '数据分析'],
        color: '#1890ff'
      },
      { 
        value: 'premium', 
        label: '高级版', 
        price: 599, 
        features: ['高级功能', '200个用户', '500个鹅群', 'AI分析', '定制报表'],
        color: '#722ed1'
      },
      { 
        value: 'enterprise', 
        label: '企业版', 
        price: 1299, 
        features: ['企业功能', '无限用户', '无限鹅群', '私有部署', '专属服务'],
        color: '#f5222d'
      }
    ],
    
    // 功能特性选项
    featureOptions: [
      { value: 'ai_analysis', label: 'AI智能分析' },
      { value: 'custom_reports', label: '定制报表' },
      { value: 'api_access', label: 'API接口' },
      { value: 'data_export', label: '数据导出' },
      { value: 'multi_language', label: '多语言支持' },
      { value: 'priority_support', label: '优先技术支持' }
    ]
  },

  async onLoad(options) {
    await this.checkPermissions();
    await this.loadTenantData();
    
    // 处理页面参数
    if (options.action === 'create') {
      this.showCreateForm();
    }
  },

  /**
   * 检查用户权限
   */
  async checkPermissions() {
    try {
      const userPermissions = await getCurrentUserPermissions();
      
      // 检查是否有租户管理权限
      if (!hasPermission(userPermissions.role, PERMISSIONS.PLATFORM_TENANT_VIEW)) {
        wx.showModal({
          title: '权限不足',
          content: '您没有访问租户管理的权限',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }
      
      this.setData({
        userPermissions
      });
      
    } catch (error) {
      console.error('权限检查失败:', error);
      wx.showToast({
        title: '权限检查失败',
        icon: 'error'
      });
    }
  },

  /**
   * 加载租户数据
   */
  async loadTenantData() {
    try {
      this.setData({ loading: true });
      
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageTenants',
          data: {
            operation: 'list',
            filters: this.data.filters,
            page: 1,
            limit: 50
          }
        }
      });
      
      if (result.result.success) {
        this.setData({
          tenantList: result.result.data.list,
          statistics: result.result.data.statistics
        });
      } else {
        wx.showToast({
          title: result.result.error || '加载失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('加载租户数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 显示创建表单
   */
  showCreateForm() {
    // 检查创建权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.PLATFORM_TENANT_CREATE)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showForm: true,
      isEditing: false,
      currentTenant: null,
      formData: {
        name: '',
        code: '',
        contact_person: '',
        contact_phone: '',
        contact_email: '',
        address: '',
        industry: 'agriculture',
        scale: 'small',
        subscription_plan: 'basic',
        max_users: 10,
        max_flocks: 50,
        status: 'active',
        description: '',
        features: []
      }
    });
  },

  /**
   * 显示编辑表单
   */
  showEditForm(e) {
    const { tenant } = e.currentTarget.dataset;
    
    // 检查编辑权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.PLATFORM_TENANT_EDIT)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showForm: true,
      isEditing: true,
      currentTenant: tenant,
      formData: {
        name: tenant.name,
        code: tenant.code,
        contact_person: tenant.contact_person || '',
        contact_phone: tenant.contact_phone || '',
        contact_email: tenant.contact_email || '',
        address: tenant.address || '',
        industry: tenant.industry || 'agriculture',
        scale: tenant.scale || 'small',
        subscription_plan: tenant.subscription_plan || 'basic',
        max_users: tenant.max_users || 10,
        max_flocks: tenant.max_flocks || 50,
        status: tenant.status || 'active',
        description: tenant.description || '',
        features: tenant.features || []
      }
    });
  },

  /**
   * 隐藏表单
   */
  hideForm() {
    this.setData({
      showForm: false,
      showConfig: false,
      showSubscription: false,
      isEditing: false,
      currentTenant: null
    });
  },

  /**
   * 表单输入处理
   */
  onFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 数字输入处理
   */
  onNumberInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: parseInt(value) || 0
    });
  },

  /**
   * 选择器变更处理
   */
  onPickerChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
    
    // 根据订阅计划自动设置限制
    if (field === 'subscription_plan') {
      this.updateSubscriptionLimits(value);
    }
  },

  /**
   * 更新订阅限制
   */
  updateSubscriptionLimits(plan) {
    const limits = {
      basic: { max_users: 10, max_flocks: 50 },
      standard: { max_users: 50, max_flocks: 200 },
      premium: { max_users: 200, max_flocks: 500 },
      enterprise: { max_users: 9999, max_flocks: 9999 }
    };
    
    const limit = limits[plan] || limits.basic;
    
    this.setData({
      'formData.max_users': limit.max_users,
      'formData.max_flocks': limit.max_flocks
    });
  },

  /**
   * 功能特性选择
   */
  onFeatureToggle(e) {
    const { feature } = e.currentTarget.dataset;
    const features = [...this.data.formData.features];
    const index = features.indexOf(feature);
    
    if (index > -1) {
      features.splice(index, 1);
    } else {
      features.push(feature);
    }
    
    this.setData({
      'formData.features': features
    });
  },

  /**
   * 保存租户信息
   */
  async saveTenant() {
    try {
      // 表单验证
      if (!this.validateForm()) {
        return;
      }
      
      this.setData({ loading: true });
      
      const operation = this.data.isEditing ? 'update' : 'create';
      const tenantData = { ...this.data.formData };
      
      if (this.data.isEditing) {
        tenantData.id = this.data.currentTenant._id;
      }
      
      const result = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageTenants',
          data: {
            operation,
            tenantData
          }
        }
      });
      
      if (result.result.success) {
        wx.showToast({
          title: this.data.isEditing ? '更新成功' : '创建成功',
          icon: 'success'
        });
        
        this.hideForm();
        await this.loadTenantData();
      } else {
        wx.showToast({
          title: result.result.error || '保存失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('保存租户失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 删除租户
   */
  async deleteTenant(e) {
    const { tenant } = e.currentTarget.dataset;
    
    // 检查删除权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.PLATFORM_TENANT_DELETE)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    try {
      const result = await wx.showModal({
        title: '确认删除',
        content: `确定要删除租户"${tenant.name}"吗？删除后无法恢复，请谨慎操作。`
      });
      
      if (!result.confirm) return;
      
      this.setData({ loading: true });
      
      const deleteResult = await wx.cloud.callFunction({
        name: 'adminManagement',
        data: {
          action: 'manageTenants',
          data: {
            operation: 'delete',
            tenantData: { id: tenant._id }
          }
        }
      });
      
      if (deleteResult.result.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        await this.loadTenantData();
      } else {
        wx.showToast({
          title: deleteResult.result.error || '删除失败',
          icon: 'error'
        });
      }
      
    } catch (error) {
      console.error('删除租户失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 管理订阅
   */
  manageSubscription(e) {
    const { tenant } = e.currentTarget.dataset;
    
    // 检查订阅管理权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.PLATFORM_TENANT_SUBSCRIPTION)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showSubscription: true,
      currentTenant: tenant
    });
  },

  /**
   * 配置租户权限
   */
  configureTenant(e) {
    const { tenant } = e.currentTarget.dataset;
    
    // 检查配置权限
    if (!hasPermission(this.data.userPermissions.role, PERMISSIONS.PLATFORM_TENANT_CONFIG)) {
      wx.showToast({
        title: '权限不足',
        icon: 'error'
      });
      return;
    }
    
    this.setData({
      showConfig: true,
      currentTenant: tenant
    });
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data;
    
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入租户名称',
        icon: 'error'
      });
      return false;
    }
    
    if (!formData.code.trim()) {
      wx.showToast({
        title: '请输入租户代码',
        icon: 'error'
      });
      return false;
    }
    
    if (formData.contact_email && !this.isValidEmail(formData.contact_email)) {
      wx.showToast({
        title: '请输入有效的邮箱地址',
        icon: 'error'
      });
      return false;
    }
    
    return true;
  },

  /**
   * 验证邮箱格式
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * 获取订阅计划信息
   */
  getSubscriptionInfo(plan) {
    return this.data.subscriptionOptions.find(option => option.value === plan) || this.data.subscriptionOptions[0];
  },

  /**
   * 筛选数据
   */
  onFilterChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`filters.${field}`]: value
    });
    
    // 重新加载数据
    this.loadTenantData();
  },

  /**
   * 搜索租户
   */
  onSearchInput(e) {
    const { value } = e.detail;
    
    this.setData({
      'filters.keyword': value
    });
    
    // 延迟搜索
    clearTimeout(this.searchTimer);
    this.searchTimer = setTimeout(() => {
      this.loadTenantData();
    }, 500);
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    await this.loadTenantData();
    wx.stopPullDownRefresh();
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '租户管理',
      path: '/pages/admin/tenant/tenant'
    };
  }
});
