/**
 * 生产页面 - 健康监测模块
 * Production Page - Health Monitoring Module
 */

const { ultimateAPIClient: apiClient } = require('../../../utils/ultimate-api-client.js');

class HealthModule {
  constructor(page) {
    this.page = page;
    this.healthRecords = [];
    this.reportData = {
      overview: {
        totalGeese: 0,
        healthyCount: 0,
        sickCount: 0,
        deathCount: 0,
        healthyRate: '0%'
      },
      diseaseStats: [],
      treatmentStats: [],
      trendData: [],
      updateTime: ''
    };
  }

  /**
   * 初始化健康模块
   */
  initialize() {
    this.loadHealthRecords();
    this.generateHealthReport();
  }

  /**
   * 加载健康记录
   */
  async loadHealthRecords() {
    try {
      console.log('[HealthModule] 开始加载健康记录');
      
      this.page.setData({ loading: true });

      // 模拟API调用
      const response = await this.fetchHealthRecords();
      
      if (response && response.data) {
        this.healthRecords = response.data;
        this.page.setData({
          healthRecords: this.healthRecords,
          loading: false
        });
        
        console.log('[HealthModule] 健康记录加载完成，记录数:', this.healthRecords.length);
      }
    } catch (error) {
      console.error('[HealthModule] 加载健康记录失败:', error);
      this.page.setData({ loading: false });
      
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  }

  /**
   * 获取健康记录数据
   */
  async fetchHealthRecords() {
    // 模拟健康记录数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: [
            {
              id: 1,
              date: '2024-01-15',
              time: '09:30',
              type: '日常检查',
              status: 'healthy',
              statusText: '健康',
              description: '鹅群状态良好，食欲正常，活动活跃',
              temperature: '38.5°C',
              weight: '3.2kg',
              notes: '无异常发现'
            },
            {
              id: 2,
              date: '2024-01-14',
              time: '14:20',
              type: '疫苗接种',
              status: 'treatment',
              statusText: '治疗中',
              description: '进行禽流感疫苗接种',
              temperature: '39.1°C',
              weight: '3.1kg',
              notes: '接种后观察24小时'
            },
            {
              id: 3,
              date: '2024-01-13',
              time: '16:45',
              type: '疾病诊断',
              status: 'sick',
              statusText: '患病',
              description: '发现部分鹅只出现腹泻症状',
              temperature: '40.2°C',
              weight: '2.9kg',
              notes: '已隔离治疗，使用抗生素'
            }
          ]
        });
      }, 800);
    });
  }

  /**
   * 生成健康报告
   */
  generateHealthReport(callback) {
    console.log('[HealthModule] 开始生成健康报告');
    
    setTimeout(() => {
      const totalGeese = 1250;
      const healthyCount = 1187;
      const sickCount = 48;
      const deathCount = 15;
      const healthyRate = ((healthyCount / totalGeese) * 100).toFixed(1) + '%';

      const overview = {
        totalGeese,
        healthyCount,
        sickCount,
        deathCount,
        healthyRate
      };

      const diseaseStats = [
        { name: '呼吸道疾病', count: 25, percentage: '52.1%' },
        { name: '消化道疾病', count: 15, percentage: '31.3%' },
        { name: '皮肤病', count: 5, percentage: '10.4%' },
        { name: '其他', count: 3, percentage: '6.2%' }
      ];

      const treatmentStats = [
        { name: '药物治疗', count: 35, percentage: '72.9%' },
        { name: '隔离观察', count: 8, percentage: '16.7%' },
        { name: '手术治疗', count: 3, percentage: '6.3%' },
        { name: '其他', count: 2, percentage: '4.1%' }
      ];

      const trendData = [
        { date: '01-09', healthy: 95.2, sick: 4.8 },
        { date: '01-10', healthy: 94.8, sick: 5.2 },
        { date: '01-11', healthy: 95.5, sick: 4.5 },
        { date: '01-12', healthy: 94.1, sick: 5.9 },
        { date: '01-13', healthy: 93.8, sick: 6.2 },
        { date: '01-14', healthy: 95.0, sick: 5.0 },
        { date: '01-15', healthy: 95.0, sick: 5.0 }
      ];

      this.reportData = {
        overview,
        diseaseStats,
        treatmentStats,
        trendData,
        updateTime: new Date().toLocaleString()
      };

      this.page.setData({
        reportData: this.reportData
      });

      // 保存健康数据到全局状态
      this.saveHealthReportDataForSharing(overview, trendData);

      console.log('[HealthModule] 健康报告生成完成');
      callback && callback();
    }, 500);
  }

  /**
   * 保存健康报告数据供其他模块使用
   */
  saveHealthReportDataForSharing(overview, trendData) {
    try {
      const app = getApp();
      if (app.globalData) {
        app.globalData.healthReportData = {
          overview,
          trendData,
          lastUpdate: new Date().toISOString()
        };
        console.log('[HealthModule] 健康数据已保存到全局状态');
      }
    } catch (error) {
      console.error('[HealthModule] 保存健康数据失败:', error);
    }
  }

  /**
   * 切换健康子标签页
   */
  onHealthTabChange(tabIndex) {
    console.log('[HealthModule] 切换健康子标签页:', tabIndex);
    
    this.page.setData({
      activeHealthTab: tabIndex
    });

    switch (tabIndex) {
      case 0: // 健康记录
        this.loadHealthRecords();
        break;
      case 1: // 健康报告
        this.generateHealthReport();
        break;
      case 2: // AI诊断
        this.initAIDiagnosis();
        break;
    }
  }

  /**
   * 初始化AI诊断
   */
  initAIDiagnosis() {
    console.log('[HealthModule] 初始化AI诊断');
    
    this.page.setData({
      symptoms: '',
      uploadedImages: [],
      diagnosisResult: null,
      isDiagnosing: false
    });
  }

  /**
   * 症状输入
   */
  onSymptomsInput(e) {
    this.page.setData({
      symptoms: e.detail.value
    });
  }

  /**
   * 上传图片
   */
  onUploadImage() {
    wx.chooseImage({
      count: 3,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;
        const currentImages = this.page.data.uploadedImages;
        
        this.page.setData({
          uploadedImages: [...currentImages, ...tempFilePaths]
        });
      }
    });
  }

  /**
   * 删除图片
   */
  onDeleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.page.data.uploadedImages;
    images.splice(index, 1);
    
    this.page.setData({
      uploadedImages: images
    });
  }

  /**
   * 开始AI诊断
   */
  async onStartDiagnosis() {
    const { symptoms, uploadedImages } = this.page.data;
    
    if (!symptoms.trim() && uploadedImages.length === 0) {
      wx.showToast({
        title: '请输入症状或上传图片',
        icon: 'none'
      });
      return;
    }

    this.page.setData({ isDiagnosing: true });

    try {
      // 模拟AI诊断
      const result = await this.performAIDiagnosis(symptoms, uploadedImages);
      
      this.page.setData({
        diagnosisResult: result,
        isDiagnosing: false
      });
    } catch (error) {
      console.error('[HealthModule] AI诊断失败:', error);
      
      this.page.setData({ isDiagnosing: false });
      
      wx.showToast({
        title: '诊断失败，请重试',
        icon: 'error'
      });
    }
  }

  /**
   * 执行AI诊断
   */
  async performAIDiagnosis(symptoms, images) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          confidence: 85,
          diagnosis: '疑似呼吸道感染',
          description: '根据描述的症状和图片分析，鹅只可能患有轻度呼吸道感染。建议进行进一步检查确认诊断。',
          recommendations: [
            '隔离患病鹅只，避免传染',
            '保持环境通风良好',
            '适当使用抗生素治疗',
            '密切观察病情变化',
            '如症状加重请及时联系兽医'
          ],
          severity: 'moderate',
          treatmentPlan: {
            medication: '阿莫西林',
            dosage: '每只每天10mg',
            duration: '连续使用5-7天',
            notes: '用药期间注意观察食欲和精神状态'
          }
        });
      }, 3000);
    });
  }

  /**
   * 切换报告类型
   */
  onReportTypeChange(e) {
    const reportType = e.currentTarget.dataset.type;
    
    this.page.setData({
      activeReportType: reportType
    });
    
    this.generateHealthReport();
  }

  /**
   * 获取健康模块数据
   */
  getData() {
    return {
      healthRecords: this.healthRecords,
      reportData: this.reportData
    };
  }

  /**
   * 销毁模块
   */
  destroy() {
    this.healthRecords = [];
    this.reportData = null;
    console.log('[HealthModule] 模块已销毁');
  }
}

module.exports = HealthModule;
