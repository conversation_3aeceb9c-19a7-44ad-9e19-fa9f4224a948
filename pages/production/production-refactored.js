/**
 * 生产页面 - 重构版本
 * Production Page - Refactored Version
 * 
 * 重构说明：
 * - 原文件 2065 行代码已拆分为 4 个功能模块
 * - 生产记录模块：production-module.js
 * - 健康监测模块：health-module.js  
 * - 物料管理模块：material-module.js
 * - 知识库模块：knowledge-module.js
 * - 模块管理器：module-manager.js
 */

const { IMAGES, UI, BUSINESS, API } = require('../../constants/index.js');
const { ultimateAPIClient: apiClient, get, post } = require('../../utils/ultimate-api-client.js');
const { RealtimeSyncMixin, QuickSetup } = require('../../utils/websocket/index.js');
const ModuleManager = require('./modules/module-manager.js');

Page(Object.assign({}, RealtimeSyncMixin, {
  data: {
    activeTab: 0,
    tabs: [
      { id: 0, name: '生产记录' },
      { id: 1, name: '健康监测' },
      { id: 2, name: '物料管理' },
      { id: 3, name: '知识库' }
    ],

    // 加载状态
    loading: true,
    refreshing: false,

    // 健康监测子标签页
    activeHealthTab: 0,
    healthTabs: [
      { id: 0, name: '健康记录' },
      { id: 1, name: '健康报告' },
      { id: 2, name: 'AI诊断' }
    ],

    // 实时同步状态
    realtimeEnabled: false,
    lastSyncTime: null,
    syncNotification: ''
  },

  /**
   * 页面加载
   */
  onLoad: function (options) {
    console.log('[Production] onLoad 开始，options:', options);
    
    // 获取传入的tab参数
    let activeTab = 0;
    if (options.tab !== undefined) {
      activeTab = parseInt(options.tab);
    }
    
    this.setData({
      activeTab: activeTab
    });

    // 初始化模块管理器
    this.initModuleManager();

    // 初始化实时同步
    this.initRealtimeSync();

    // 加载对应标签页的数据
    this.loadTabData(activeTab);
  },

  /**
   * 页面显示
   */
  onShow: function () {
    // 检查全局目标标签页
    const app = getApp();
    if (app.globalData && app.globalData.targetTab !== undefined) {
      console.log('[Production] 检测到全局目标标签页:', app.globalData.targetTab);
      this.setData({
        activeTab: app.globalData.targetTab
      });
      app.globalData.targetTab = undefined;
    }

    // 刷新当前标签页数据
    this.refreshCurrentTabData();
  },

  /**
   * 初始化模块管理器
   */
  initModuleManager: function() {
    try {
      this.moduleManager = new ModuleManager(this);
      this.moduleManager.initialize();
      console.log('[Production] 模块管理器初始化完成');
    } catch (error) {
      console.error('[Production] 模块管理器初始化失败:', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'error'
      });
    }
  },

  /**
   * 切换Tab
   */
  onTabChange: function (e) {
    const index = e.detail.index;
    if (typeof index === 'number' && index >= 0) {
      this.setData({
        activeTab: index
      });
      this.loadTabData(index);
    }
  },

  /**
   * 加载标签页数据
   */
  loadTabData: function(tabIndex) {
    if (!this.moduleManager) {
      console.error('[Production] 模块管理器未初始化');
      return;
    }

    console.log('[Production] 加载标签页数据:', tabIndex);

    const moduleMap = {
      0: 'production',  // 生产记录
      1: 'health',      // 健康监测
      2: 'material',    // 物料管理
      3: 'knowledge'    // 知识库
    };

    const moduleKey = moduleMap[tabIndex];
    if (moduleKey) {
      try {
        this.moduleManager.switchModule(moduleKey);
        console.log(`[Production] 已切换到模块: ${moduleKey}`);
      } catch (error) {
        console.error(`[Production] 切换模块失败: ${moduleKey}`, error);
      }
    }
  },

  /**
   * 刷新当前标签页数据
   */
  refreshCurrentTabData: function() {
    const currentTab = this.data.activeTab;
    this.loadTabData(currentTab);
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    console.log('[Production] 下拉刷新');
    
    this.setData({ refreshing: true });
    
    // 刷新当前模块数据
    if (this.moduleManager) {
      const currentModule = this.moduleManager.getCurrentModule();
      if (currentModule && currentModule.refresh) {
        currentModule.refresh().finally(() => {
          this.setData({ refreshing: false });
          wx.stopPullDownRefresh();
        });
      } else {
        this.refreshCurrentTabData();
        setTimeout(() => {
          this.setData({ refreshing: false });
          wx.stopPullDownRefresh();
        }, 1000);
      }
    } else {
      setTimeout(() => {
        this.setData({ refreshing: false });
        wx.stopPullDownRefresh();
      }, 1000);
    }
  },

  // ==================== 健康监测相关方法 ====================

  /**
   * 切换健康子标签页
   */
  onHealthTabChange: function(e) {
    const tabIndex = e.detail.index;
    const healthModule = this.moduleManager.getModule('health');
    
    if (healthModule && healthModule.onHealthTabChange) {
      healthModule.onHealthTabChange(tabIndex);
    }
  },

  /**
   * 症状输入
   */
  onSymptomsInput: function(e) {
    const healthModule = this.moduleManager.getModule('health');
    if (healthModule && healthModule.onSymptomsInput) {
      healthModule.onSymptomsInput(e);
    }
  },

  /**
   * 上传图片
   */
  onUploadImage: function() {
    const healthModule = this.moduleManager.getModule('health');
    if (healthModule && healthModule.onUploadImage) {
      healthModule.onUploadImage();
    }
  },

  /**
   * 删除图片
   */
  onDeleteImage: function(e) {
    const healthModule = this.moduleManager.getModule('health');
    if (healthModule && healthModule.onDeleteImage) {
      healthModule.onDeleteImage(e);
    }
  },

  /**
   * 开始AI诊断
   */
  onStartDiagnosis: function() {
    const healthModule = this.moduleManager.getModule('health');
    if (healthModule && healthModule.onStartDiagnosis) {
      healthModule.onStartDiagnosis();
    }
  },

  /**
   * 切换报告类型
   */
  onReportTypeChange: function(e) {
    const healthModule = this.moduleManager.getModule('health');
    if (healthModule && healthModule.onReportTypeChange) {
      healthModule.onReportTypeChange(e);
    }
  },

  // ==================== 物料管理相关方法 ====================

  /**
   * 切换物料标签页
   */
  onMaterialTabChange: function(e) {
    const materialModule = this.moduleManager.getModule('material');
    if (materialModule && materialModule.onMaterialTabChange) {
      materialModule.onMaterialTabChange(e);
    }
  },

  /**
   * 物料项点击
   */
  onMaterialItemTap: function(e) {
    const materialModule = this.moduleManager.getModule('material');
    if (materialModule && materialModule.onMaterialItemTap) {
      materialModule.onMaterialItemTap(e);
    }
  },

  /**
   * 添加物料
   */
  onAddMaterial: function() {
    const materialModule = this.moduleManager.getModule('material');
    if (materialModule && materialModule.onAddMaterial) {
      materialModule.onAddMaterial();
    }
  },

  /**
   * 库存预警
   */
  onStockWarning: function() {
    const materialModule = this.moduleManager.getModule('material');
    if (materialModule && materialModule.onStockWarning) {
      materialModule.onStockWarning();
    }
  },

  // ==================== 知识库相关方法 ====================

  /**
   * 分类切换
   */
  onCategoryChange: function(e) {
    const knowledgeModule = this.moduleManager.getModule('knowledge');
    if (knowledgeModule && knowledgeModule.onCategoryChange) {
      knowledgeModule.onCategoryChange(e);
    }
  },

  /**
   * 搜索文章
   */
  onSearchInput: function(e) {
    const knowledgeModule = this.moduleManager.getModule('knowledge');
    if (knowledgeModule && knowledgeModule.onSearchInput) {
      knowledgeModule.onSearchInput(e);
    }
  },

  /**
   * 文章点击
   */
  onArticleTap: function(e) {
    const knowledgeModule = this.moduleManager.getModule('knowledge');
    if (knowledgeModule && knowledgeModule.onArticleTap) {
      knowledgeModule.onArticleTap(e);
    }
  },

  /**
   * 文章收藏
   */
  onArticleLike: function(e) {
    const knowledgeModule = this.moduleManager.getModule('knowledge');
    if (knowledgeModule && knowledgeModule.onArticleLike) {
      knowledgeModule.onArticleLike(e);
    }
  },

  // ==================== 生产记录相关方法 ====================

  /**
   * 记录类型过滤
   */
  onRecordTypeFilter: function(e) {
    const productionModule = this.moduleManager.getModule('production');
    if (productionModule && productionModule.onRecordTypeFilter) {
      productionModule.onRecordTypeFilter(e);
    }
  },

  /**
   * 搜索记录
   */
  onSearchRecord: function(e) {
    const productionModule = this.moduleManager.getModule('production');
    if (productionModule && productionModule.onSearchRecord) {
      productionModule.onSearchRecord(e);
    }
  },

  /**
   * 记录点击
   */
  onRecordTap: function(e) {
    const productionModule = this.moduleManager.getModule('production');
    if (productionModule && productionModule.onRecordTap) {
      productionModule.onRecordTap(e);
    }
  },

  /**
   * 关闭记录详情
   */
  onCloseRecordDetail: function() {
    const productionModule = this.moduleManager.getModule('production');
    if (productionModule && productionModule.onCloseRecordDetail) {
      productionModule.onCloseRecordDetail();
    }
  },

  /**
   * 添加记录
   */
  onAddRecord: function() {
    const productionModule = this.moduleManager.getModule('production');
    if (productionModule && productionModule.onAddRecord) {
      productionModule.onAddRecord();
    }
  },

  // ==================== 实时同步相关方法 ====================
  // (这些方法已在之前的代码中实现)

  /**
   * 页面卸载
   */
  onUnload: function () {
    console.log('[Production] onUnload 页面卸载');
    
    // 清理模块管理器
    if (this.moduleManager) {
      this.moduleManager.destroy();
      this.moduleManager = null;
    }
    
    // 清理实时同步
    this.cleanupRealtimeSync();
  }
}));
