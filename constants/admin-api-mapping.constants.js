/**
 * 管理后台API端点映射配置
 * Admin Backend API Endpoint Mapping Configuration
 * 
 * 基于Context7最佳实践的完整云函数到HTTP API映射
 * 支持平台级管理、租户管理、系统监控等所有管理功能
 * 
 * 映射规则：
 * 1. REST风格API设计
 * 2. 统一的错误处理和响应格式
 * 3. 完整的权限验证和数据隔离
 * 4. 操作审计日志记录
 */

/**
 * 管理后台云函数映射表
 * 将HTTP API端点映射到对应的云函数和操作
 */
const ADMIN_API_MAPPING = {
  // ========== 仪表板和统计 ==========
  'GET /admin/dashboard/stats': {
    cloudFunction: 'adminManagement',
    action: 'getDashboardStats',
    description: '获取管理后台仪表板统计数据',
    requiredPermissions: ['PLATFORM_DASHBOARD_VIEW']
  },
  
  'GET /admin/dashboard/realtime': {
    cloudFunction: 'adminManagement', 
    action: 'getRealTimeStats',
    description: '获取实时统计数据',
    requiredPermissions: ['PLATFORM_DASHBOARD_VIEW']
  },
  
  // ========== 系统健康监控 ==========
  'GET /admin/system/health': {
    cloudFunction: 'adminManagement',
    action: 'getSystemHealth',
    description: '获取系统健康状态',
    requiredPermissions: ['PLATFORM_SYSTEM_MONITOR']
  },
  
  'POST /admin/system/health/check': {
    cloudFunction: 'adminManagement',
    action: 'checkSystemComponents',
    description: '执行系统组件检查',
    requiredPermissions: ['PLATFORM_SYSTEM_MONITOR']
  },
  
  'GET /admin/system/performance': {
    cloudFunction: 'adminManagement',
    action: 'getPerformanceMetrics',
    description: '获取系统性能指标',
    requiredPermissions: ['PLATFORM_SYSTEM_MONITOR']
  },
  
  // ========== 系统配置管理 ==========
  'GET /admin/system/config': {
    cloudFunction: 'systemManagement',
    action: 'getConfig',
    description: '获取系统配置',
    requiredPermissions: ['PLATFORM_CONFIG_VIEW']
  },
  
  'PUT /admin/system/config/:configId': {
    cloudFunction: 'systemManagement',
    action: 'updateConfig',
    description: '更新系统配置',
    requiredPermissions: ['PLATFORM_CONFIG_EDIT']
  },
  
  'GET /admin/system/config/list': {
    cloudFunction: 'systemManagement',
    action: 'getConfigList',
    description: '获取系统配置列表',
    requiredPermissions: ['PLATFORM_CONFIG_VIEW']
  },
  
  // ========== 平台公告管理 ==========
  'GET /admin/announcements': {
    cloudFunction: 'systemManagement',
    action: 'getAnnouncements',
    description: '获取平台公告列表',
    requiredPermissions: ['PLATFORM_ANNOUNCEMENT_VIEW']
  },
  
  'POST /admin/announcements': {
    cloudFunction: 'systemManagement',
    action: 'createAnnouncement',
    description: '创建平台公告',
    requiredPermissions: ['PLATFORM_ANNOUNCEMENT_CREATE']
  },
  
  'PUT /admin/announcements/:announcementId': {
    cloudFunction: 'systemManagement',
    action: 'updateAnnouncement',
    description: '更新平台公告',
    requiredPermissions: ['PLATFORM_ANNOUNCEMENT_EDIT']
  },
  
  'DELETE /admin/announcements/:announcementId': {
    cloudFunction: 'systemManagement',
    action: 'deleteAnnouncement',
    description: '删除平台公告',
    requiredPermissions: ['PLATFORM_ANNOUNCEMENT_DELETE']
  },
  
  'POST /admin/announcements/:announcementId/publish': {
    cloudFunction: 'systemManagement',
    action: 'publishAnnouncement',
    description: '发布平台公告',
    requiredPermissions: ['PLATFORM_ANNOUNCEMENT_PUBLISH']
  },
  
  // ========== 知识库管理 ==========
  'GET /admin/knowledge': {
    cloudFunction: 'systemManagement',
    action: 'getKnowledgeList',
    description: '获取知识库列表',
    requiredPermissions: ['PLATFORM_KNOWLEDGE_VIEW']
  },
  
  'POST /admin/knowledge': {
    cloudFunction: 'systemManagement',
    action: 'createKnowledge',
    description: '创建知识库内容',
    requiredPermissions: ['PLATFORM_KNOWLEDGE_CREATE']
  },
  
  'PUT /admin/knowledge/:knowledgeId': {
    cloudFunction: 'systemManagement',
    action: 'updateKnowledge',
    description: '更新知识库内容',
    requiredPermissions: ['PLATFORM_KNOWLEDGE_EDIT']
  },
  
  'DELETE /admin/knowledge/:knowledgeId': {
    cloudFunction: 'systemManagement',
    action: 'deleteKnowledge',
    description: '删除知识库内容',
    requiredPermissions: ['PLATFORM_KNOWLEDGE_DELETE']
  },
  
  // ========== 租户管理 ==========
  'GET /admin/tenants': {
    cloudFunction: 'tenantManagement',
    action: 'getTenantList',
    description: '获取租户列表',
    requiredPermissions: ['PLATFORM_TENANT_VIEW']
  },
  
  'POST /admin/tenants': {
    cloudFunction: 'tenantManagement',
    action: 'createTenant',
    description: '创建新租户',
    requiredPermissions: ['PLATFORM_TENANT_CREATE']
  },
  
  'GET /admin/tenants/:tenantId': {
    cloudFunction: 'tenantManagement',
    action: 'getTenantDetail',
    description: '获取租户详情',
    requiredPermissions: ['PLATFORM_TENANT_VIEW']
  },
  
  'PUT /admin/tenants/:tenantId': {
    cloudFunction: 'tenantManagement',
    action: 'updateTenant',
    description: '更新租户信息',
    requiredPermissions: ['PLATFORM_TENANT_EDIT']
  },
  
  'PUT /admin/tenants/:tenantId/subscription': {
    cloudFunction: 'tenantManagement',
    action: 'updateTenantSubscription',
    description: '更新租户订阅',
    requiredPermissions: ['PLATFORM_TENANT_SUBSCRIPTION']
  },
  
  'POST /admin/tenants/:tenantId/suspend': {
    cloudFunction: 'tenantManagement',
    action: 'suspendTenant',
    description: '暂停租户',
    requiredPermissions: ['PLATFORM_TENANT_SUSPEND']
  },
  
  'POST /admin/tenants/:tenantId/activate': {
    cloudFunction: 'tenantManagement',
    action: 'activateTenant',
    description: '激活租户',
    requiredPermissions: ['PLATFORM_TENANT_ACTIVATE']
  },
  
  'GET /admin/tenants/stats': {
    cloudFunction: 'tenantManagement',
    action: 'getTenantStats',
    description: '获取租户统计信息',
    requiredPermissions: ['PLATFORM_TENANT_STATS']
  },
  
  'GET /admin/tenants/:tenantId/usage': {
    cloudFunction: 'tenantManagement',
    action: 'getTenantUsage',
    description: '获取租户使用情况',
    requiredPermissions: ['PLATFORM_TENANT_USAGE']
  },
  
  // ========== 今日鹅价管理 ==========
  'GET /admin/goose-price': {
    cloudFunction: 'goosePriceManagement',
    action: 'getPriceList',
    description: '获取鹅价列表',
    requiredPermissions: ['PLATFORM_GOOSE_PRICE_VIEW']
  },
  
  'POST /admin/goose-price': {
    cloudFunction: 'goosePriceManagement',
    action: 'createPrice',
    description: '发布新鹅价',
    requiredPermissions: ['PLATFORM_GOOSE_PRICE_PUBLISH']
  },
  
  'PUT /admin/goose-price/:priceId': {
    cloudFunction: 'goosePriceManagement',
    action: 'updatePrice',
    description: '更新鹅价信息',
    requiredPermissions: ['PLATFORM_GOOSE_PRICE_EDIT']
  },
  
  'DELETE /admin/goose-price/:priceId': {
    cloudFunction: 'goosePriceManagement',
    action: 'deletePrice',
    description: '删除鹅价记录',
    requiredPermissions: ['PLATFORM_GOOSE_PRICE_DELETE']
  },
  
  'GET /admin/goose-price/history': {
    cloudFunction: 'goosePriceManagement',
    action: 'getPriceHistory',
    description: '获取鹅价历史趋势',
    requiredPermissions: ['PLATFORM_GOOSE_PRICE_VIEW']
  },
  
  'GET /admin/goose-price/analysis': {
    cloudFunction: 'goosePriceManagement',
    action: 'getPriceAnalysis',
    description: '获取鹅价分析报告',
    requiredPermissions: ['PLATFORM_GOOSE_PRICE_ANALYSIS']
  },
  
  // ========== 用户和权限管理 ==========
  'GET /admin/users': {
    cloudFunction: 'userManagement',
    action: 'getUserList',
    description: '获取用户列表',
    requiredPermissions: ['PLATFORM_USER_VIEW']
  },
  
  'POST /admin/users': {
    cloudFunction: 'userManagement',
    action: 'createUser',
    description: '创建新用户',
    requiredPermissions: ['PLATFORM_USER_CREATE']
  },
  
  'GET /admin/users/:userId': {
    cloudFunction: 'userManagement',
    action: 'getUserDetail',
    description: '获取用户详情',
    requiredPermissions: ['PLATFORM_USER_VIEW']
  },
  
  'PUT /admin/users/:userId': {
    cloudFunction: 'userManagement',
    action: 'updateUser',
    description: '更新用户信息',
    requiredPermissions: ['PLATFORM_USER_EDIT']
  },
  
  'PUT /admin/users/:userId/role': {
    cloudFunction: 'userManagement',
    action: 'updateUserRole',
    description: '更新用户角色',
    requiredPermissions: ['PLATFORM_USER_ROLE_EDIT']
  },
  
  'POST /admin/users/:userId/suspend': {
    cloudFunction: 'userManagement',
    action: 'suspendUser',
    description: '暂停用户',
    requiredPermissions: ['PLATFORM_USER_SUSPEND']
  },
  
  'POST /admin/users/:userId/activate': {
    cloudFunction: 'userManagement',
    action: 'activateUser',
    description: '激活用户',
    requiredPermissions: ['PLATFORM_USER_ACTIVATE']
  },
  
  // ========== 权限使用统计 ==========
  'GET /admin/permissions/usage': {
    cloudFunction: 'adminManagement',
    action: 'getPermissionUsageStats',
    description: '获取权限使用统计',
    requiredPermissions: ['PLATFORM_PERMISSION_STATS']
  },
  
  'GET /admin/users/activity': {
    cloudFunction: 'adminManagement',
    action: 'getUserActivityStats',
    description: '获取用户活动统计',
    requiredPermissions: ['PLATFORM_USER_ACTIVITY_STATS']
  },
  
  // ========== 系统警告和通知 ==========
  'GET /admin/alerts': {
    cloudFunction: 'adminManagement',
    action: 'getSystemAlerts',
    description: '获取系统警告列表',
    requiredPermissions: ['PLATFORM_ALERT_VIEW']
  },
  
  'POST /admin/alerts': {
    cloudFunction: 'adminManagement',
    action: 'createSystemAlert',
    description: '创建系统警告',
    requiredPermissions: ['PLATFORM_ALERT_CREATE']
  },
  
  'PUT /admin/alerts/:alertId/status': {
    cloudFunction: 'adminManagement',
    action: 'updateAlertStatus',
    description: '更新警告状态',
    requiredPermissions: ['PLATFORM_ALERT_HANDLE']
  },
  
  'GET /admin/notifications': {
    cloudFunction: 'adminManagement',
    action: 'getSystemNotifications',
    description: '获取系统通知',
    requiredPermissions: ['PLATFORM_NOTIFICATION_VIEW']
  },
  
  'POST /admin/notifications': {
    cloudFunction: 'adminManagement',
    action: 'createSystemNotification',
    description: '创建系统通知',
    requiredPermissions: ['PLATFORM_NOTIFICATION_CREATE']
  },
  
  // ========== 最近活动和操作日志 ==========
  'GET /admin/activities/recent': {
    cloudFunction: 'adminManagement',
    action: 'getRecentActivities',
    description: '获取最近活动',
    requiredPermissions: ['PLATFORM_ACTIVITY_VIEW']
  },
  
  'GET /admin/logs/operations': {
    cloudFunction: 'auditLog',
    action: 'getOperationLogs',
    description: '获取操作日志',
    requiredPermissions: ['PLATFORM_LOG_VIEW']
  },
  
  'GET /admin/logs/audit': {
    cloudFunction: 'auditLog',
    action: 'getSecurityAuditReport',
    description: '获取审计日志',
    requiredPermissions: ['PLATFORM_AUDIT_LOG_VIEW']
  },

  'POST /admin/logs/operation': {
    cloudFunction: 'auditLog',
    action: 'logUserOperation',
    description: '记录用户操作',
    requiredPermissions: ['PLATFORM_LOG_CREATE']
  },

  'POST /admin/permissions/verify': {
    cloudFunction: 'auditLog',
    action: 'verifyUserPermission',
    description: '验证用户权限',
    requiredPermissions: ['PLATFORM_PERMISSION_VERIFY']
  },

  'GET /admin/users/:userId/permissions': {
    cloudFunction: 'auditLog',
    action: 'getUserPermissionDetails',
    description: '获取用户权限详情',
    requiredPermissions: ['PLATFORM_USER_PERMISSION_VIEW']
  },
  
  // ========== 数据分析和报表 ==========
  'GET /admin/analytics/platform': {
    cloudFunction: 'analytics',
    action: 'getPlatformAnalytics',
    description: '获取平台数据分析',
    requiredPermissions: ['PLATFORM_ANALYTICS_VIEW']
  },
  
  'GET /admin/analytics/tenant/:tenantId': {
    cloudFunction: 'analytics',
    action: 'getTenantAnalytics',
    description: '获取租户数据分析',
    requiredPermissions: ['PLATFORM_TENANT_ANALYTICS']
  },

  'POST /admin/analytics/report': {
    cloudFunction: 'analytics',
    action: 'generateReport',
    description: '生成数据报表',
    requiredPermissions: ['PLATFORM_DATA_EXPORT']
  },

  'GET /admin/analytics/charts': {
    cloudFunction: 'analytics',
    action: 'getChartData',
    description: '获取图表数据',
    requiredPermissions: ['PLATFORM_ANALYTICS_VIEW']
  },

  // ========== 系统健康监控 ==========
  'POST /admin/health/check': {
    cloudFunction: 'healthMonitoring',
    action: 'performHealthCheck',
    description: '执行系统健康检查',
    requiredPermissions: ['PLATFORM_HEALTH_CHECK']
  },

  'GET /admin/system/metrics': {
    cloudFunction: 'healthMonitoring',
    action: 'getSystemMetrics',
    description: '获取系统监控指标',
    requiredPermissions: ['PLATFORM_MONITORING_VIEW']
  },
  
  'GET /admin/charts/monitoring': {
    cloudFunction: 'adminManagement',
    action: 'getMonitoringChartData',
    description: '获取监控图表数据',
    requiredPermissions: ['PLATFORM_CHART_VIEW']
  },
  
  // ========== 数据导出 ==========
  'POST /admin/export/platform': {
    cloudFunction: 'adminManagement',
    action: 'exportPlatformData',
    description: '导出平台数据',
    requiredPermissions: ['PLATFORM_DATA_EXPORT']
  },
  
  'GET /admin/export/history': {
    cloudFunction: 'exportManagement',
    action: 'getExportHistory',
    description: '获取导出历史',
    requiredPermissions: ['PLATFORM_EXPORT_HISTORY']
  },
  
  // ========== AI模型配置 ==========
  'GET /admin/ai/configs': {
    cloudFunction: 'aiConfigManagement',
    action: 'getAIConfigs',
    description: '获取AI模型配置',
    requiredPermissions: ['PLATFORM_AI_CONFIG_VIEW']
  },
  
  'POST /admin/ai/configs': {
    cloudFunction: 'aiConfigManagement',
    action: 'createAIConfig',
    description: '创建AI模型配置',
    requiredPermissions: ['PLATFORM_AI_CONFIG_CREATE']
  },
  
  'PUT /admin/ai/configs/:configId': {
    cloudFunction: 'aiConfigManagement',
    action: 'updateAIConfig',
    description: '更新AI模型配置',
    requiredPermissions: ['PLATFORM_AI_CONFIG_EDIT']
  },
  
  'GET /admin/ai/usage': {
    cloudFunction: 'aiConfigManagement',
    action: 'getAIUsageStats',
    description: '获取AI使用统计',
    requiredPermissions: ['PLATFORM_AI_USAGE_STATS']
  },
  
  // ========== 商城配置管理 ==========
  'GET /admin/shop/config': {
    cloudFunction: 'shopConfigManagement',
    action: 'getShopConfig',
    description: '获取商城配置',
    requiredPermissions: ['PLATFORM_SHOP_CONFIG_VIEW']
  },
  
  'PUT /admin/shop/config': {
    cloudFunction: 'shopConfigManagement',
    action: 'updateShopConfig',
    description: '更新商城配置',
    requiredPermissions: ['PLATFORM_SHOP_CONFIG_EDIT']
  },
  
  'GET /admin/shop/products': {
    cloudFunction: 'shopConfigManagement',
    action: 'getShopProducts',
    description: '获取商城商品',
    requiredPermissions: ['PLATFORM_SHOP_PRODUCT_VIEW']
  },
  
  'POST /admin/shop/products': {
    cloudFunction: 'shopConfigManagement',
    action: 'createShopProduct',
    description: '创建商城商品',
    requiredPermissions: ['PLATFORM_SHOP_PRODUCT_CREATE']
  },
  
  'GET /admin/shop/stats': {
    cloudFunction: 'shopConfigManagement',
    action: 'getShopStats',
    description: '获取商城统计',
    requiredPermissions: ['PLATFORM_SHOP_STATS_VIEW']
  },
  
  // ========== 系统备份和恢复 ==========
  'POST /admin/backup/create': {
    cloudFunction: 'backupManagement',
    action: 'createBackup',
    description: '创建系统备份',
    requiredPermissions: ['PLATFORM_BACKUP_CREATE']
  },
  
  'GET /admin/backup/list': {
    cloudFunction: 'backupManagement',
    action: 'getBackupList',
    description: '获取备份列表',
    requiredPermissions: ['PLATFORM_BACKUP_VIEW']
  },
  
  'POST /admin/backup/:backupId/restore': {
    cloudFunction: 'backupManagement',
    action: 'restoreBackup',
    description: '恢复系统备份',
    requiredPermissions: ['PLATFORM_BACKUP_RESTORE']
  },
  
  'DELETE /admin/backup/:backupId': {
    cloudFunction: 'backupManagement',
    action: 'deleteBackup',
    description: '删除系统备份',
    requiredPermissions: ['PLATFORM_BACKUP_DELETE']
  }
};

/**
 * 权限级别定义
 * 用于API权限验证的权限级别映射
 */
const PERMISSION_LEVELS = {
  // 平台级权限（超级管理员专用）
  PLATFORM_SUPER_ADMIN: ['super_admin'],
  
  // 平台管理权限
  PLATFORM_ADMIN: ['super_admin', 'platform_admin'],
  
  // 租户管理权限
  TENANT_ADMIN: ['super_admin', 'platform_admin', 'tenant_admin'],
  
  // 普通管理权限
  ADMIN: ['super_admin', 'platform_admin', 'tenant_admin', 'admin'],
  
  // 只读权限
  READONLY: ['super_admin', 'platform_admin', 'tenant_admin', 'admin', 'manager']
};

/**
 * API分组配置
 * 用于管理界面的功能分组展示
 */
const API_GROUPS = {
  dashboard: {
    name: '仪表板管理',
    description: '系统概览、实时统计、性能监控',
    icon: 'dashboard',
    endpoints: [
      'GET /admin/dashboard/stats',
      'GET /admin/dashboard/realtime',
      'GET /admin/system/performance'
    ]
  },
  
  system: {
    name: '系统管理',
    description: '系统配置、健康监控、组件管理',
    icon: 'system',
    endpoints: [
      'GET /admin/system/health',
      'POST /admin/system/health/check',
      'GET /admin/system/config',
      'PUT /admin/system/config/:configId'
    ]
  },
  
  content: {
    name: '内容管理',
    description: '公告管理、知识库、鹅价发布',
    icon: 'content',
    endpoints: [
      'GET /admin/announcements',
      'POST /admin/announcements',
      'GET /admin/knowledge',
      'GET /admin/goose-price'
    ]
  },
  
  tenant: {
    name: '租户管理',
    description: '租户注册、订阅管理、使用监控',
    icon: 'tenant',
    endpoints: [
      'GET /admin/tenants',
      'POST /admin/tenants',
      'PUT /admin/tenants/:tenantId/subscription',
      'GET /admin/tenants/stats'
    ]
  },
  
  user: {
    name: '用户管理',
    description: '用户账号、角色权限、活动监控',
    icon: 'user',
    endpoints: [
      'GET /admin/users',
      'POST /admin/users',
      'PUT /admin/users/:userId/role',
      'GET /admin/users/activity'
    ]
  },
  
  monitoring: {
    name: '监控告警',
    description: '系统告警、操作日志、活动监控',
    icon: 'monitoring',
    endpoints: [
      'GET /admin/alerts',
      'POST /admin/alerts',
      'GET /admin/logs/operations',
      'GET /admin/activities/recent'
    ]
  },
  
  analytics: {
    name: '数据分析',
    description: '平台分析、租户分析、图表展示',
    icon: 'analytics',
    endpoints: [
      'GET /admin/analytics/platform',
      'GET /admin/analytics/tenant/:tenantId',
      'GET /admin/charts/monitoring'
    ]
  },
  
  config: {
    name: '高级配置',
    description: 'AI配置、商城管理、系统备份',
    icon: 'config',
    endpoints: [
      'GET /admin/ai/configs',
      'GET /admin/shop/config',
      'POST /admin/backup/create'
    ]
  }
};

/**
 * 导出映射配置
 */
module.exports = {
  ADMIN_API_MAPPING,
  PERMISSION_LEVELS,
  API_GROUPS,
  
  /**
   * 获取指定端点的映射配置
   */
  getEndpointMapping: (method, path) => {
    const key = `${method.toUpperCase()} ${path}`;
    return ADMIN_API_MAPPING[key];
  },
  
  /**
   * 获取所有端点列表
   */
  getAllEndpoints: () => {
    return Object.keys(ADMIN_API_MAPPING);
  },
  
  /**
   * 根据云函数名称获取相关端点
   */
  getEndpointsByCloudFunction: (cloudFunction) => {
    return Object.entries(ADMIN_API_MAPPING)
      .filter(([, config]) => config.cloudFunction === cloudFunction)
      .map(([endpoint]) => endpoint);
  },
  
  /**
   * 根据权限获取可访问的端点
   */
  getEndpointsByPermission: (permissions) => {
    return Object.entries(ADMIN_API_MAPPING)
      .filter(([, config]) => {
        return config.requiredPermissions.some(perm => permissions.includes(perm));
      })
      .map(([endpoint]) => endpoint);
  },
  
  /**
   * 验证端点访问权限
   */
  validateEndpointPermission: (endpoint, userPermissions) => {
    const config = ADMIN_API_MAPPING[endpoint];
    if (!config) return false;
    
    return config.requiredPermissions.some(perm => userPermissions.includes(perm));
  }
};