# 微信小程序组件路径诊断报告

## 🔍 诊断概览
- 扫描文件: 73个
- 发现问题: 14个
- 组件引用总数: 30个

## ❌ 发现的问题

### 按问题类型分类
- **COMPONENT_UNUSED**: 14个

### 详细问题列表

#### 1. COMPONENT_UNUSED
- **文件**: `pages/dev-tools/permission-performance-test/permission-performance-test.json`
- **组件**: `permission-check`
- **路径**: `/components/permission-check/permission-check`
- **问题**: 组件已声明但未使用: permission-check




#### 2. COMPONENT_UNUSED
- **文件**: `pages/shop/shop.json`
- **组件**: `c-section-header`
- **路径**: `/components/section-header/section-header`
- **问题**: 组件已声明但未使用: c-section-header




#### 3. COMPONENT_UNUSED
- **文件**: `pages/shop/shop.json`
- **组件**: `c-list-item`
- **路径**: `/components/list-item/list-item`
- **问题**: 组件已声明但未使用: c-list-item




#### 4. COMPONENT_UNUSED
- **文件**: `pages/shop/shop.json`
- **组件**: `c-card`
- **路径**: `/components/common/card/card`
- **问题**: 组件已声明但未使用: c-card




#### 5. COMPONENT_UNUSED
- **文件**: `pages/shop/shop.json`
- **组件**: `c-empty-state`
- **路径**: `/components/common/empty-state/empty-state`
- **问题**: 组件已声明但未使用: c-empty-state




#### 6. COMPONENT_UNUSED
- **文件**: `pages/shop/shop.json`
- **组件**: `c-loading`
- **路径**: `/components/common/loading/loading`
- **问题**: 组件已声明但未使用: c-loading




#### 7. COMPONENT_UNUSED
- **文件**: `pages/workspace/activity/apply/apply.json`
- **组件**: `permission-check`
- **路径**: `/components/permission-check/permission-check`
- **问题**: 组件已声明但未使用: permission-check




#### 8. COMPONENT_UNUSED
- **文件**: `pages/workspace/contract/apply/apply.json`
- **组件**: `permission-check`
- **路径**: `/components/permission-check/permission-check`
- **问题**: 组件已声明但未使用: permission-check




#### 9. COMPONENT_UNUSED
- **文件**: `pages/workspace/payment/apply/apply.json`
- **组件**: `permission-check`
- **路径**: `/components/permission-check/permission-check`
- **问题**: 组件已声明但未使用: permission-check




#### 10. COMPONENT_UNUSED
- **文件**: `pages/workspace/purchase/apply/apply.json`
- **组件**: `permission-check`
- **路径**: `/components/permission-check/permission-check`
- **问题**: 组件已声明但未使用: permission-check




#### 11. COMPONENT_UNUSED
- **文件**: `pages/workspace/reserve/apply/apply.json`
- **组件**: `permission-check`
- **路径**: `/components/permission-check/permission-check`
- **问题**: 组件已声明但未使用: permission-check




#### 12. COMPONENT_UNUSED
- **文件**: `pages/workspace/workspace.json`
- **组件**: `permission-check`
- **路径**: `/components/permission-check/permission-check`
- **问题**: 组件已声明但未使用: permission-check




#### 13. COMPONENT_UNUSED
- **文件**: `pages/workspace/workspace.json`
- **组件**: `loading-state`
- **路径**: `/components/workspace/loading-state/loading-state`
- **问题**: 组件已声明但未使用: loading-state




#### 14. COMPONENT_UNUSED
- **文件**: `pages/workspace/workspace.json`
- **组件**: `data-card`
- **路径**: `/components/workspace/data-card/data-card`
- **问题**: 组件已声明但未使用: data-card




## 🔧 修复建议

### 1. 组件不存在的修复
对于 `COMPONENT_NOT_FOUND` 错误：
- 检查组件路径是否正确
- 确认组件文件是否存在
- 考虑移除未使用的组件引用

### 2. 组件不完整的修复
对于 `COMPONENT_INCOMPLETE` 错误：
- 补充缺失的组件文件（.js, .wxml, .json）
- 检查组件是否正确实现

### 3. 未使用组件的清理
对于 `COMPONENT_UNUSED` 警告：
- 从配置文件中移除未使用的组件声明
- 减少小程序包体积

## 📊 组件使用统计
- **permission-check**: 使用9次
- **c-loading**: 使用4次
- **c-empty-state**: 使用3次
- **c-section-header**: 使用3次
- **c-card**: 使用2次
- **form-field-input**: 使用1次
- **c-weather-compact**: 使用1次
- **c-goose-price**: 使用1次
- **c-tab-bar**: 使用1次
- **c-record-detail-modal**: 使用1次

## 🛠️ 自动修复脚本

```javascript
// 移除未使用的组件引用
const unusedComponents = [
  {
    "type": "COMPONENT_UNUSED",
    "configFile": "pages/dev-tools/permission-performance-test/permission-performance-test.json",
    "componentName": "permission-check",
    "componentPath": "/components/permission-check/permission-check",
    "message": "组件已声明但未使用: permission-check"
  },
  {
    "type": "COMPONENT_UNUSED",
    "configFile": "pages/shop/shop.json",
    "componentName": "c-section-header",
    "componentPath": "/components/section-header/section-header",
    "message": "组件已声明但未使用: c-section-header"
  },
  {
    "type": "COMPONENT_UNUSED",
    "configFile": "pages/shop/shop.json",
    "componentName": "c-list-item",
    "componentPath": "/components/list-item/list-item",
    "message": "组件已声明但未使用: c-list-item"
  },
  {
    "type": "COMPONENT_UNUSED",
    "configFile": "pages/shop/shop.json",
    "componentName": "c-card",
    "componentPath": "/components/common/card/card",
    "message": "组件已声明但未使用: c-card"
  },
  {
    "type": "COMPONENT_UNUSED",
    "configFile": "pages/shop/shop.json",
    "componentName": "c-empty-state",
    "componentPath": "/components/common/empty-state/empty-state",
    "message": "组件已声明但未使用: c-empty-state"
  },
  {
    "type": "COMPONENT_UNUSED",
    "configFile": "pages/shop/shop.json",
    "componentName": "c-loading",
    "componentPath": "/components/common/loading/loading",
    "message": "组件已声明但未使用: c-loading"
  },
  {
    "type": "COMPONENT_UNUSED",
    "configFile": "pages/workspace/activity/apply/apply.json",
    "componentName": "permission-check",
    "componentPath": "/components/permission-check/permission-check",
    "message": "组件已声明但未使用: permission-check"
  },
  {
    "type": "COMPONENT_UNUSED",
    "configFile": "pages/workspace/contract/apply/apply.json",
    "componentName": "permission-check",
    "componentPath": "/components/permission-check/permission-check",
    "message": "组件已声明但未使用: permission-check"
  },
  {
    "type": "COMPONENT_UNUSED",
    "configFile": "pages/workspace/payment/apply/apply.json",
    "componentName": "permission-check",
    "componentPath": "/components/permission-check/permission-check",
    "message": "组件已声明但未使用: permission-check"
  },
  {
    "type": "COMPONENT_UNUSED",
    "configFile": "pages/workspace/purchase/apply/apply.json",
    "componentName": "permission-check",
    "componentPath": "/components/permission-check/permission-check",
    "message": "组件已声明但未使用: permission-check"
  },
  {
    "type": "COMPONENT_UNUSED",
    "configFile": "pages/workspace/reserve/apply/apply.json",
    "componentName": "permission-check",
    "componentPath": "/components/permission-check/permission-check",
    "message": "组件已声明但未使用: permission-check"
  },
  {
    "type": "COMPONENT_UNUSED",
    "configFile": "pages/workspace/workspace.json",
    "componentName": "permission-check",
    "componentPath": "/components/permission-check/permission-check",
    "message": "组件已声明但未使用: permission-check"
  },
  {
    "type": "COMPONENT_UNUSED",
    "configFile": "pages/workspace/workspace.json",
    "componentName": "loading-state",
    "componentPath": "/components/workspace/loading-state/loading-state",
    "message": "组件已声明但未使用: loading-state"
  },
  {
    "type": "COMPONENT_UNUSED",
    "configFile": "pages/workspace/workspace.json",
    "componentName": "data-card",
    "componentPath": "/components/workspace/data-card/data-card",
    "message": "组件已声明但未使用: data-card"
  }
];

// 修复组件路径
const pathIssues = [];
```

## 🎯 修复优先级

### 🚨 高优先级（必须修复）
- COMPONENT_NOT_FOUND: 0个
- JSON_PARSE_ERROR: 0个

### ⚠️ 中优先级（建议修复）
- COMPONENT_INCOMPLETE: 0个

### 💡 低优先级（优化建议）
- COMPONENT_UNUSED: 14个

---
诊断时间: 2025/8/31 12:39:47
