{"title": "智慧养鹅云开发 - API文档", "version": "3.0.0", "modules": [{"name": "enhanced-permission-manager", "file": "utils/enhanced-permission-manager.js", "description": "增强的权限管理系统 - 优化版\n  Enhanced Permission Management System - Optimized Version\n  \n  新增功能：\n  - 权限缓存机制\n  - 性能优化\n  - 角色继承\n  - 权限审计日志\n  - 动态权限配置", "methods": [{"name": "checkPermission", "description": "增强的权限管理系统 - 优化版\n  Enhanced Permission Management System - Optimized Version\n  \n  新增功能：\n  - 权限缓存机制\n  - 性能优化\n  - 角色继承\n  - 权限审计日志\n  - 动态权限配置"}, {"name": "calculatePermission", "description": "计算用户权限\n    @param {Object} user 用户对象\n    @param {string} permission 权限标识\n    @returns {Promise<boolean>}"}, {"name": "getRolePermissions", "description": "获取角色权限（带缓存）\n    @param {string} role 角色\n    @returns {Promise<Array>}"}, {"name": "getInheritedRoles", "description": "获取角色继承链\n    @param {string} role 角色\n    @returns {Array}"}, {"name": "checkContextPermission", "description": "检查上下文权限\n    @param {Object} user 用户对象\n    @param {string} permission 权限标识\n    @returns {Promise<boolean>}"}, {"name": "checkMultiplePermissions", "description": "批量检查权限\n    @param {Object} user 用户对象\n    @param {Array} permissions 权限数组\n    @returns {Promise<Object>}"}, {"name": "getUserPermissionInfo", "description": "获取用户完整权限信息\n    @param {Object} user 用户对象\n    @returns {Promise<Object>}"}, {"name": "clearUserCache", "description": "清除用户权限缓存\n    @param {string} userId 用户ID"}, {"name": "clearRoleCache", "description": "清除角色缓存\n    @param {string} role 角色"}, {"name": "logPermissionCheck", "description": "记录权限检查日志\n    @param {Object} user 用户对象\n    @param {string} permission 权限标识\n    @param {boolean} result 检查结果\n    @param {string} source 来源（cache/calculated）"}, {"name": "isSensitivePermission", "description": "判断是否为敏感权限\n    @param {string} permission 权限标识\n    @returns {boolean}"}, {"name": "persistAuditLog", "description": "持久化审计日志\n    @param {Object} logEntry 日志条目"}, {"name": "getPermissionStats", "description": "获取权限统计信息\n    @returns {Object}"}, {"name": "initCacheCleanup", "description": "初始化缓存清理定时器"}, {"name": "cleanupExpiredCache", "description": "清理过期缓存"}, {"name": "checkPermissionLegacy", "description": "向后兼容方法 - 与现有代码集成"}, {"name": "hasRole", "description": "兼容原有的hasRole函数\n    @param {Object} user 用户对象\n    @param {string} role 角色\n    @returns {boolean}"}, {"name": "hasAnyPermission", "description": "批量权限检查 - 兼容现有API\n    @param {Object} user 用户对象\n    @param {Array} permissions 权限数组\n    @returns {Promise<boolean>}"}, {"name": "hasAllPermissions", "description": "检查所有权限 - 兼容现有API\n    @param {Object} user 用户对象\n    @param {Array} permissions 权限数组\n    @returns {Promise<boolean>}"}, {"name": "getAccessibleMenuItems", "description": "获取用户可访问的菜单项\n    @param {Object} user 用户对象\n    @param {Array} menuItems 菜单项配置\n    @returns {Promise<Array>}"}, {"name": "requirePermissions", "description": "权限装饰器 - 用于页面和组件权限控制\n    @param {string|Array} requiredPermissions 必需权限\n    @param {Function} callback 回调函数\n    @returns {Function}"}, {"name": "requireRoles", "description": "角色装饰器 - 用于角色权限控制\n    @param {string|Array} requiredRoles 必需角色\n    @param {Function} callback 回调函数\n    @returns {Function}"}, {"name": "getStats", "description": "获取权限管理统计信息\n    @returns {Object} 统计信息"}]}, {"name": "ultimate-api-client", "file": "utils/ultimate-api-client.js", "description": "终极API客户端 - 统一API调用接口\n  Ultimate API Client - Unified API Interface\n  \n  整合所有API调用逻辑，支持云函数和HTTP双模式\n  提供业务模块化的API接口", "methods": [{"name": "smartApiCall", "description": "终极API客户端 - 统一API调用接口\n  Ultimate API Client - Unified API Interface\n  \n  整合所有API调用逻辑，支持云函数和HTTP双模式\n  提供业务模块化的API接口"}, {"name": "httpApiCall", "description": "HTTP API调用"}, {"name": "toRelative", "description": "转换为相对URL - 微信小程序兼容版本"}, {"name": "get", "description": "GET请求"}, {"name": "post", "description": "POST请求"}, {"name": "put", "description": "PUT请求"}, {"name": "delete", "description": "DELETE请求"}, {"name": "clearCache", "description": "清理缓存"}, {"name": "getStats", "description": "获取统计信息"}]}, {"name": "enhanced-data-isolation", "file": "utils/enhanced-data-isolation.js", "description": "增强的数据隔离系统 - 优化版\n  Enhanced Data Isolation System - Optimized Version\n  \n  新增功能：\n  - 查询结果缓存\n  - 自动查询优化\n  - 批量操作支持\n  - 性能监控\n  - 数据访问审计", "methods": [{"name": "createSecureQuery", "description": "增强的数据隔离系统 - 优化版\n  Enhanced Data Isolation System - Optimized Version\n  \n  新增功能：\n  - 查询结果缓存\n  - 自动查询优化\n  - 批量操作支持\n  - 性能监控\n  - 数据访问审计"}, {"name": "buildSecureQuery", "description": "构建安全查询条件\n    @param {Object} user 用户对象\n    @param {string} collection 集合名称\n    @param {Object} baseQuery 基础查询条件\n    @param {Object} options 查询选项\n    @returns {Promise<Object>}"}, {"name": "buildIsolationQuery", "description": "构建隔离查询条件\n    @param {Object} user 用户对象\n    @param {Object} config 集合配置\n    @param {Object} options 查询选项\n    @returns {Promise<Object>}"}, {"name": "buildPlatformQuery", "description": "构建平台级查询\n    @param {Object} user 用户对象\n    @param {Object} options 查询选项\n    @returns {Promise<Object>}"}, {"name": "buildTenantQuery", "description": "构建租户级查询\n    @param {Object} user 用户对象\n    @param {Object} options 查询选项\n    @returns {Promise<Object>}"}, {"name": "buildUserQuery", "description": "构建用户级查询\n    @param {Object} user 用户对象\n    @param {Object} options 查询选项\n    @returns {Promise<Object>}"}, {"name": "buildSharedQuery", "description": "构建共享数据查询\n    @param {Object} user 用户对象\n    @param {Object} options 查询选项\n    @returns {Promise<Object>}"}, {"name": "validateWriteData", "description": "验证数据写入安全性（增强版）\n    @param {Object} user 用户对象\n    @param {string} collection 集合名称\n    @param {Object} data 数据对象\n    @param {Object} options 选项\n    @returns {Promise<Object>}"}, {"name": "validateDataByLevel", "description": "根据隔离级别验证数据\n    @param {Object} user 用户对象\n    @param {Object} config 集合配置\n    @param {Object} data 数据对象\n    @param {Object} options 选项\n    @returns {Promise<Object>}"}, {"name": "validatePlatformData", "description": "验证平台级数据\n    @param {Object} user 用户对象\n    @param {Object} data 数据对象\n    @param {Object} options 选项\n    @returns {Promise<Object>}"}, {"name": "validateTenantData", "description": "验证租户级数据\n    @param {Object} user 用户对象\n    @param {Object} data 数据对象\n    @param {Object} options 选项\n    @returns {Promise<Object>}"}, {"name": "validateUserData", "description": "验证用户级数据\n    @param {Object} user 用户对象\n    @param {Object} data 数据对象\n    @param {Object} options 选项\n    @returns {Promise<Object>}"}, {"name": "validateSharedData", "description": "验证共享数据\n    @param {Object} user 用户对象\n    @param {Object} data 数据对象\n    @param {Object} options 选项\n    @returns {Promise<Object>}"}, {"name": "batchOperations", "description": "批量操作支持\n    @param {Object} user 用户对象\n    @param {string} collection 集合名称\n    @param {Array} operations 操作数组\n    @param {Object} options 选项\n    @returns {Promise<Array>}"}]}, {"name": "unified-config-manager", "file": "utils/unified-config-manager.js", "description": "统一配置管理系统 - 第三阶段实现\n  Unified Configuration Management System - Phase 3 Implementation\n  \n  功能包括：\n  - 环境配置管理\n  - 动态配置更新\n  - 配置缓存机制\n  - 配置验证\n  - 多租户配置隔离", "methods": [{"name": "init", "description": "统一配置管理系统 - 第三阶段实现\n  Unified Configuration Management System - Phase 3 Implementation\n  \n  功能包括：\n  - 环境配置管理\n  - 动态配置更新\n  - 配置缓存机制\n  - 配置验证\n  - 多租户配置隔离"}, {"name": "detectEnvironment", "description": "检测运行环境"}, {"name": "mergeEnvironmentConfigs", "description": "合并环境配置"}, {"name": "loadRemoteConfigs", "description": "加载远程配置"}, {"name": "loadTenantConfigs", "description": "加载租户配置"}, {"name": "get", "description": "获取配置值\n    @param {string} key 配置键，支持点号分隔的路径\n    @param {} defaultValue 默认值\n    @returns {} 配置值"}, {"name": "set", "description": "设置配置值\n    @param {string} key 配置键\n    @param {} value 配置值\n    @param {boolean} persist 是否持久化到服务器"}, {"name": "getNestedValue", "description": "获取嵌套对象的值"}, {"name": "setNestedValue", "description": "设置嵌套对象的值"}, {"name": "deepMerge", "description": "深度合并对象"}, {"name": "persistConfig", "description": "持久化配置到服务器"}, {"name": "getAll", "description": "获取所有配置"}, {"name": "getByPrefix", "description": "获取指定前缀的所有配置"}, {"name": "getAllKeys", "description": "获取所有配置键"}, {"name": "validateConfig", "description": "验证配置"}, {"name": "reset", "description": "重置配置"}, {"name": "startConfigSync", "description": "启动配置同步"}, {"name": "onChange", "description": "监听配置变更"}, {"name": "offChange", "description": "移除配置监听"}, {"name": "emitConfigChange", "description": "触发配置变更事件"}, {"name": "getStats", "description": "获取配置统计信息"}, {"name": "export", "description": "导出配置"}, {"name": "toYAML", "description": "简化的YAML转换"}, {"name": "destroy", "description": "销毁配置管理器"}]}, {"name": "enhanced-health-module", "file": "pages/production/modules/enhanced-health-module.js", "description": "增强健康监测模块 - 第三阶段重构版本\n  Enhanced Health Monitoring Module - Phase 3 Refactored Version\n  \n  功能包括：\n  - 健康记录管理\n  - AI诊断功能\n  - 健康报告生成\n  - 数据统计分析\n  - 实时数据同步", "methods": [{"name": "init", "description": "增强健康监测模块 - 第三阶段重构版本\n  Enhanced Health Monitoring Module - Phase 3 Refactored Version\n  \n  功能包括：\n  - 健康记录管理\n  - AI诊断功能\n  - 健康报告生成\n  - 数据统计分析\n  - 实时数据同步"}, {"name": "checkPermissions", "description": "检查用户权限"}, {"name": "filterTabsByPermissions", "description": "根据权限过滤标签页"}, {"name": "initializeData", "description": "初始化数据"}, {"name": "setupEventListeners", "description": "设置事件监听"}, {"name": "loadInitialData", "description": "加载初始数据"}, {"name": "onHealthTabChange", "description": "健康标签页切换"}, {"name": "loadTabData", "description": "根据标签页加载数据"}, {"name": "loadHealthRecords", "description": "加载健康记录（增强版）"}, {"name": "processHealthRecords", "description": "处理健康记录数据"}, {"name": "mapStatusToDisplay", "description": "状态映射显示"}, {"name": "onAddRecord", "description": "添加健康记录"}, {"name": "onViewRecord", "description": "查看健康记录详情"}, {"name": "onSymptomInput", "description": "AI诊断相关方法（增强版）"}, {"name": "chooseImages", "description": "Promise化的选择图片"}, {"name": "callAIDiagnosis", "description": "调用AI诊断服务（增强版）"}, {"name": "uploadDiagnosisImages", "description": "上传诊断图片"}, {"name": "processAIDiagnosisResult", "description": "处理AI诊断结果"}, {"name": "saveDiagnosisHistory", "description": "保存诊断历史"}, {"name": "generateMockDiagnosisResult", "description": "生成模拟诊断结果"}, {"name": "onClearResult", "description": "清除诊断结果"}, {"name": "loadDiagnosisHistory", "description": "加载诊断历史"}, {"name": "onReportTypeChange", "description": "健康报告相关方法（增强版）"}, {"name": "onExportReport", "description": "导出健康报告（增强版）"}, {"name": "destroy", "description": "销毁模块"}]}, {"name": "enhanced-material-module", "file": "pages/production/modules/enhanced-material-module.js", "description": "增强物料管理模块 - 第三阶段重构版本\n  Enhanced Material Management Module - Phase 3 Refactored Version\n  \n  功能包括：\n  - 物料库存管理\n  - 入库出库记录\n  - 库存预警\n  - 采购管理\n  - 成本核算\n  - 智能补货建议", "methods": [{"name": "init", "description": "增强物料管理模块 - 第三阶段重构版本\n  Enhanced Material Management Module - Phase 3 Refactored Version\n  \n  功能包括：\n  - 物料库存管理\n  - 入库出库记录\n  - 库存预警\n  - 采购管理\n  - 成本核算\n  - 智能补货建议"}, {"name": "checkPermissions", "description": "检查用户权限"}, {"name": "filterTabsByPermissions", "description": "根据权限过滤标签页"}, {"name": "initializeData", "description": "初始化数据"}, {"name": "setupEventListeners", "description": "设置事件监听"}, {"name": "loadInitialData", "description": "加载初始数据"}, {"name": "startPeriodicTasks", "description": "启动定时任务"}, {"name": "onMaterialTabChange", "description": "物料标签页切换"}, {"name": "loadMaterialList", "description": "加载物料列表（增强版）"}, {"name": "buildSortQuery", "description": "构建排序查询"}, {"name": "processMaterialData", "description": "处理物料数据（增强版）"}, {"name": "calculateMaterialStatus", "description": "计算物料状态（增强版）"}, {"name": "calculateTurnoverRate", "description": "计算周转率"}, {"name": "filterMaterialList", "description": "过滤物料列表（增强版）"}, {"name": "calculateMaterialStats", "description": "计算物料统计数据（增强版）"}, {"name": "calculateMonthlyConsumption", "description": "计算月消耗量（增强版）"}, {"name": "calculateAvgTurnoverRate", "description": "计算平均周转率"}, {"name": "calculateAvgCostPerUnit", "description": "计算平均单位成本"}, {"name": "onSearchInput", "description": "搜索物料"}, {"name": "onSortChange", "description": "排序切换"}, {"name": "onStatusFilterChange", "description": "状态过滤"}, {"name": "destroy", "description": "销毁模块"}]}, {"name": "enhanced-production", "file": "pages/production/enhanced-production.js", "description": "增强生产管理页面 - 第三阶段重构版本\n  Enhanced Production Management Page - Phase 3 Refactored Version\n  \n  采用模块化架构，整合所有生产相关功能：\n  - 健康监测模块\n  - 物料管理模块\n  - 生产记录模块\n  - 知识库模块", "methods": [{"name": "onLoad", "description": "增强生产管理页面 - 第三阶段重构版本\n  Enhanced Production Management Page - Phase 3 Refactored Version\n  \n  采用模块化架构，整合所有生产相关功能：\n  - 健康监测模块\n  - 物料管理模块\n  - 生产记录模块\n  - 知识库模块"}, {"name": "initializePage", "description": "初始化页面"}, {"name": "loadPageConfig", "description": "加载页面配置"}, {"name": "checkUserPermissions", "description": "检查用户权限"}, {"name": "getTabRequiredPermissions", "description": "获取标签页所需权限"}, {"name": "initializeModules", "description": "初始化模块"}, {"name": "initializeModule", "description": "初始化单个模块"}, {"name": "loadPageData", "description": "加载页面数据"}, {"name": "loadOverviewStats", "description": "加载概览统计数据"}, {"name": "setupPageConfig", "description": "设置页面配置"}, {"name": "onTabChange", "description": "标签页切换"}, {"name": "onQuickAction", "description": "快捷操作处理"}, {"name": "onGlobalSearch", "description": "全局搜索"}, {"name": "performGlobalSearch", "description": "执行全局搜索"}, {"name": "onPullDownRefresh", "description": "下拉刷新"}, {"name": "onReachBottom", "description": "上拉加载更多"}, {"name": "startAutoSave", "description": "启动自动保存"}, {"name": "performAutoSave", "description": "执行自动保存"}, {"name": "handlePageError", "description": "处理页面错误"}, {"name": "onUnload", "description": "页面卸载"}]}]}