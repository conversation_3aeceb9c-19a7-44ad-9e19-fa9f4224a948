/**
 * 租户上下文管理器
 * 管理多租户环境下的上下文切换和权限控制
 */

class TenantContextManager {
  constructor() {
    this.currentTenant = null;
    this.platformMode = false;
    this.listeners = [];
    
    // 从本地存储恢复上下文
    this.restoreContext();
  }
  
  /**
   * 从本地存储恢复上下文
   */
  restoreContext() {
    try {
      const storedTenant = wx.getStorageSync('current_tenant');
      const storedPlatformMode = wx.getStorageSync('platform_mode');
      
      if (storedTenant) {
        this.currentTenant = storedTenant;
        this.platformMode = false;
      } else if (storedPlatformMode) {
        this.platformMode = true;
        this.currentTenant = null;
      }
      
      console.log('租户上下文已恢复:', {
        tenant: this.currentTenant?.tenant_name,
        platformMode: this.platformMode
      });
    } catch (error) {
      console.error('恢复租户上下文失败:', error);
    }
  }
  
  /**
   * 设置租户上下文
   * @param {Object} tenantInfo 租户信息
   */
  setTenantContext(tenantInfo) {
    if (!tenantInfo || !tenantInfo.tenant_id) {
      throw new Error('租户信息无效');
    }
    
    this.currentTenant = {
      tenant_id: tenantInfo.tenant_id,
      tenant_name: tenantInfo.tenant_name,
      subscription_plan: tenantInfo.subscription_plan,
      features: tenantInfo.features || [],
      status: tenantInfo.status,
      admin_user_id: tenantInfo.admin_user_id,
      switched_at: Date.now()
    };
    
    this.platformMode = false;
    
    // 持久化到本地存储
    wx.setStorageSync('current_tenant', this.currentTenant);
    wx.removeStorageSync('platform_mode');
    
    // 通知监听者
    this.notifyContextChange('TENANT_SWITCHED', this.currentTenant);
    
    console.log('租户上下文已切换:', this.currentTenant.tenant_name);
  }
  
  /**
   * 切换到平台管理模式
   */
  switchToPlatformMode() {
    this.platformMode = true;
    this.currentTenant = null;
    
    // 持久化到本地存储
    wx.setStorageSync('platform_mode', true);
    wx.removeStorageSync('current_tenant');
    
    // 通知监听者
    this.notifyContextChange('PLATFORM_MODE_ENABLED', null);
    
    console.log('已切换到平台管理模式');
  }
  
  /**
   * 退出平台模式，返回租户模式
   */
  exitPlatformMode() {
    this.platformMode = false;
    
    wx.removeStorageSync('platform_mode');
    
    // 通知监听者
    this.notifyContextChange('PLATFORM_MODE_DISABLED', null);
    
    console.log('已退出平台管理模式');
  }
  
  /**
   * 获取当前租户信息
   * @returns {Object|null} 当前租户信息
   */
  getCurrentTenant() {
    return this.platformMode ? null : this.currentTenant;
  }
  
  /**
   * 检查是否为平台模式
   * @returns {Boolean}
   */
  isPlatformMode() {
    return this.platformMode;
  }
  
  /**
   * 获取当前上下文模式
   * @returns {String} 'platform' | 'tenant' | 'none'
   */
  getCurrentMode() {
    if (this.platformMode) return 'platform';
    if (this.currentTenant) return 'tenant';
    return 'none';
  }
  
  /**
   * 验证租户访问权限
   * @param {String} requiredTenantId 需要的租户ID
   * @returns {Boolean}
   */
  validateTenantAccess(requiredTenantId) {
    // 平台模式可以访问所有租户
    if (this.platformMode) {
      return true;
    }
    
    // 检查当前租户
    if (!this.currentTenant) {
      throw new Error('未设置租户上下文');
    }
    
    // 验证租户ID匹配
    if (requiredTenantId && this.currentTenant.tenant_id !== requiredTenantId) {
      throw new Error('无权访问其他租户数据');
    }
    
    return true;
  }
  
  /**
   * 检查租户功能权限
   * @param {String} feature 功能名称
   * @returns {Boolean}
   */
  hasFeatureAccess(feature) {
    if (this.platformMode) return true;
    
    if (!this.currentTenant) return false;
    
    const features = this.currentTenant.features || [];
    return features.includes(feature);
  }
  
  /**
   * 获取租户配置
   * @param {String} key 配置键名
   * @returns {Any} 配置值
   */
  getTenantConfig(key) {
    if (!this.currentTenant || !this.currentTenant.config) {
      return null;
    }
    
    return this.currentTenant.config[key];
  }
  
  /**
   * 添加上下文变更监听器
   * @param {Function} listener 监听器函数
   * @returns {Function} 取消监听的函数
   */
  addContextChangeListener(listener) {
    if (typeof listener !== 'function') {
      throw new Error('监听器必须是函数');
    }
    
    this.listeners.push(listener);
    
    // 返回取消监听的函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }
  
  /**
   * 通知上下文变更
   * @param {String} event 事件类型
   * @param {Any} data 事件数据
   */
  notifyContextChange(event, data) {
    const contextInfo = {
      event,
      data,
      currentTenant: this.currentTenant,
      platformMode: this.platformMode,
      timestamp: Date.now()
    };
    
    // 通知所有监听器
    this.listeners.forEach(listener => {
      try {
        listener(contextInfo);
      } catch (error) {
        console.error('上下文变更监听器错误:', error);
      }
    });
    
    // 发送全局事件（如果在小程序环境中）
    if (typeof getApp === 'function') {
      try {
        const app = getApp();
        if (app.globalData && app.globalData.eventBus) {
          app.globalData.eventBus.emit('tenantContextChanged', contextInfo);
        }
      } catch (error) {
        console.warn('发送全局事件失败:', error);
      }
    }
  }
  
  /**
   * 清除所有上下文
   */
  clearContext() {
    this.currentTenant = null;
    this.platformMode = false;
    
    // 清理本地存储
    wx.removeStorageSync('current_tenant');
    wx.removeStorageSync('platform_mode');
    
    // 通知监听者
    this.notifyContextChange('CONTEXT_CLEARED', null);
    
    console.log('租户上下文已清除');
  }
  
  /**
   * 获取上下文摘要信息
   * @returns {Object} 上下文摘要
   */
  getContextSummary() {
    return {
      mode: this.getCurrentMode(),
      tenant: this.currentTenant ? {
        id: this.currentTenant.tenant_id,
        name: this.currentTenant.tenant_name,
        plan: this.currentTenant.subscription_plan,
        status: this.currentTenant.status
      } : null,
      platformMode: this.platformMode,
      listenersCount: this.listeners.length
    };
  }
  
  /**
   * 导出上下文配置
   * @returns {Object} 可序列化的上下文配置
   */
  exportContext() {
    return {
      currentTenant: this.currentTenant,
      platformMode: this.platformMode,
      exportedAt: Date.now()
    };
  }
  
  /**
   * 导入上下文配置
   * @param {Object} contextData 上下文配置数据
   */
  importContext(contextData) {
    if (!contextData) {
      throw new Error('上下文数据无效');
    }
    
    this.currentTenant = contextData.currentTenant || null;
    this.platformMode = contextData.platformMode || false;
    
    // 同步到本地存储
    if (this.currentTenant) {
      wx.setStorageSync('current_tenant', this.currentTenant);
    }
    if (this.platformMode) {
      wx.setStorageSync('platform_mode', true);
    }
    
    // 通知变更
    this.notifyContextChange('CONTEXT_IMPORTED', contextData);
    
    console.log('上下文配置已导入');
  }
}

// 创建并导出单例
const tenantContextManager = new TenantContextManager();

export default tenantContextManager;

/**
 * 便捷的上下文操作方法
 */
export const TenantContext = {
  // 设置租户
  setTenant: (tenantInfo) => tenantContextManager.setTenantContext(tenantInfo),
  
  // 切换到平台模式
  toPlatform: () => tenantContextManager.switchToPlatformMode(),
  
  // 获取当前租户
  current: () => tenantContextManager.getCurrentTenant(),
  
  // 检查模式
  isPlatform: () => tenantContextManager.isPlatformMode(),
  
  // 验证权限
  validate: (tenantId) => tenantContextManager.validateTenantAccess(tenantId),
  
  // 检查功能
  hasFeature: (feature) => tenantContextManager.hasFeatureAccess(feature),
  
  // 获取配置
  getConfig: (key) => tenantContextManager.getTenantConfig(key),
  
  // 清除上下文
  clear: () => tenantContextManager.clearContext()
};

/**
 * 使用示例：
 * 
 * // 设置租户上下文
 * TenantContext.setTenant({
 *   tenant_id: 'tenant_001',
 *   tenant_name: '示例养殖场',
 *   subscription_plan: 'premium'
 * });
 * 
 * // 检查当前模式
 * if (TenantContext.isPlatform()) {
 *   // 平台管理员操作
 * } else {
 *   // 租户用户操作
 *   const tenant = TenantContext.current();
 * }
 * 
 * // 验证权限
 * TenantContext.validate('tenant_001');
 */