/**
 * 数据隔离管理器
 * 实现多租户环境下的严格数据隔离和访问控制
 */

import TenantContextManager from './tenant-context.js';

/**
 * 数据隔离级别枚举
 */
export const ISOLATION_LEVELS = {
  PLATFORM: 'platform',    // 平台级数据（所有租户可见）
  TENANT: 'tenant',        // 租户级数据（租户内隔离）
  USER: 'user',           // 用户级数据（用户私有）
  SHARED: 'shared'        // 共享数据（有条件共享）
};

/**
 * 数据隔离管理器类
 */
class DataIsolationManager {
  constructor() {
    // 集合的隔离级别配置
    this.collectionIsolationMap = new Map([
      // 平台级数据
      ['goose_prices', ISOLATION_LEVELS.PLATFORM],
      ['platform_announcements', ISOLATION_LEVELS.PLATFORM],
      ['knowledge_base', ISOLATION_LEVELS.SHARED],
      ['tenants', ISOLATION_LEVELS.PLATFORM],
      ['ai_configurations', ISOLATION_LEVELS.PLATFORM],
      ['system_config', ISOLATION_LEVELS.PLATFORM],
      
      // 租户级数据
      ['flocks', ISOLATION_LEVELS.TENANT],
      ['materials', ISOLATION_LEVELS.TENANT],
      ['health_records', ISOLATION_LEVELS.TENANT],
      ['financial_records', ISOLATION_LEVELS.TENANT],
      ['production_records', ISOLATION_LEVELS.TENANT],
      
      // 用户级数据
      ['user_preferences', ISOLATION_LEVELS.USER],
      ['user_notifications', ISOLATION_LEVELS.USER],
      ['user_sessions', ISOLATION_LEVELS.USER],
      
      // 共享数据
      ['shop_products', ISOLATION_LEVELS.SHARED],
      ['orders', ISOLATION_LEVELS.TENANT],
      ['workspace_documents', ISOLATION_LEVELS.TENANT]
    ]);
    
    // 操作权限映射
    this.operationPermissions = new Map([
      ['create', ['admin', 'manager', 'employee']],
      ['read', ['admin', 'manager', 'employee', 'viewer']],
      ['update', ['admin', 'manager']],
      ['delete', ['admin']],
      ['export', ['admin', 'manager']],
      ['import', ['admin']]
    ]);
  }
  
  /**
   * 创建安全查询条件
   * @param {Object} user 用户信息
   * @param {String} collection 集合名称
   * @param {Object} baseQuery 基础查询条件
   * @returns {Object} 安全的查询条件
   */
  createSecureQuery(user, collection, baseQuery = {}) {
    if (!user) {
      throw new Error('用户信息缺失，无法创建安全查询');
    }
    
    const query = { ...baseQuery };
    const isolationLevel = this.getCollectionIsolationLevel(collection);
    
    switch (isolationLevel) {
      case ISOLATION_LEVELS.PLATFORM:
        // 平台级数据：只有平台管理员可以访问
        if (!this.isPlatformAdmin(user)) {
          throw new Error('无权访问平台级数据');
        }
        break;
        
      case ISOLATION_LEVELS.TENANT:
        // 租户级数据：自动注入租户ID过滤
        if (!user.tenant_id) {
          throw new Error('用户租户信息缺失');
        }
        query.tenant_id = user.tenant_id;
        break;
        
      case ISOLATION_LEVELS.USER:
        // 用户级数据：只能访问自己的数据
        query.user_id = user._id;
        if (user.tenant_id) {
          query.tenant_id = user.tenant_id;
        }
        break;
        
      case ISOLATION_LEVELS.SHARED:
        // 共享数据：根据具体业务规则处理
        query = this.handleSharedDataQuery(user, collection, query);
        break;
        
      default:
        console.warn(`未知的数据隔离级别: ${isolationLevel} for ${collection}`);
    }
    
    return query;
  }
  
  /**
   * 验证数据访问权限
   * @param {Object} user 用户信息
   * @param {Object} data 要访问的数据
   * @param {String} operation 操作类型
   * @returns {Boolean}
   */
  validateDataAccess(user, data, operation) {
    if (!user) {
      throw new Error('用户信息缺失');
    }
    
    if (!data) {
      throw new Error('数据对象缺失');
    }
    
    // 检查租户隔离
    if (data.tenant_id && data.tenant_id !== user.tenant_id && !this.isPlatformAdmin(user)) {
      throw new Error('无权访问其他租户的数据');
    }
    
    // 检查用户级隔离
    if (data.user_id && data.user_id !== user._id && !this.canAccessUserData(user, data)) {
      throw new Error('无权访问其他用户的数据');
    }
    
    // 检查操作权限
    if (!this.hasOperationPermission(user, operation)) {
      throw new Error(`无权执行操作: ${operation}`);
    }
    
    return true;
  }
  
  /**
   * 创建安全的数据记录
   * @param {Object} user 用户信息
   * @param {String} collection 集合名称
   * @param {Object} data 数据对象
   * @returns {Object} 安全的数据记录
   */
  createSecureRecord(user, collection, data) {
    const secureData = { ...data };
    const isolationLevel = this.getCollectionIsolationLevel(collection);
    
    // 自动注入隔离字段
    switch (isolationLevel) {
      case ISOLATION_LEVELS.TENANT:
        secureData.tenant_id = user.tenant_id;
        break;
        
      case ISOLATION_LEVELS.USER:
        secureData.user_id = user._id;
        secureData.tenant_id = user.tenant_id;
        break;
    }
    
    // 添加审计字段
    secureData.created_by = user._id;
    secureData.created_at = new Date();
    secureData.updated_by = user._id;
    secureData.updated_at = new Date();
    
    return secureData;
  }
  
  /**
   * 更新数据记录的审计信息
   * @param {Object} user 用户信息
   * @param {Object} updateData 更新数据
   * @returns {Object} 包含审计信息的更新数据
   */
  updateSecureRecord(user, updateData) {
    const secureUpdateData = { ...updateData };
    
    // 添加更新审计信息
    secureUpdateData.updated_by = user._id;
    secureUpdateData.updated_at = new Date();
    
    // 防止恶意修改隔离字段
    delete secureUpdateData.tenant_id;
    delete secureUpdateData.user_id;
    delete secureUpdateData.created_by;
    delete secureUpdateData.created_at;
    
    return secureUpdateData;
  }
  
  /**
   * 获取集合的隔离级别
   * @param {String} collection 集合名称
   * @returns {String} 隔离级别
   */
  getCollectionIsolationLevel(collection) {
    return this.collectionIsolationMap.get(collection) || ISOLATION_LEVELS.TENANT;
  }
  
  /**
   * 检查是否为平台管理员
   * @param {Object} user 用户信息
   * @returns {Boolean}
   */
  isPlatformAdmin(user) {
    const platformRoles = ['SUPER_ADMIN', 'PLATFORM_ADMIN'];
    return user && user.role && platformRoles.includes(user.role);
  }
  
  /**
   * 检查是否有操作权限
   * @param {Object} user 用户信息
   * @param {String} operation 操作类型
   * @returns {Boolean}
   */
  hasOperationPermission(user, operation) {
    if (!user || !user.role) return false;
    
    const allowedRoles = this.operationPermissions.get(operation);
    if (!allowedRoles) return false;
    
    // 平台管理员拥有所有权限
    if (this.isPlatformAdmin(user)) return true;
    
    // 检查用户角色权限
    const userRole = user.role.toLowerCase();
    return allowedRoles.includes(userRole);
  }
  
  /**
   * 检查是否可以访问用户数据
   * @param {Object} accessor 访问者用户信息
   * @param {Object} data 被访问的数据
   * @returns {Boolean}
   */
  canAccessUserData(accessor, data) {
    // 平台管理员可以访问所有数据
    if (this.isPlatformAdmin(accessor)) return true;
    
    // 租户管理员可以访问本租户用户的数据
    if (accessor.role === 'TENANT_ADMIN' && accessor.tenant_id === data.tenant_id) {
      return true;
    }
    
    // 经理可以访问下属的数据（需要实现组织架构）
    if (accessor.role === 'MANAGER' && this.isSubordinate(accessor, data)) {
      return true;
    }
    
    return false;
  }
  
  /**
   * 处理共享数据查询
   * @param {Object} user 用户信息
   * @param {String} collection 集合名称
   * @param {Object} query 查询条件
   * @returns {Object} 处理后的查询条件
   */
  handleSharedDataQuery(user, collection, query) {
    switch (collection) {
      case 'knowledge_base':
        // 知识库：可以访问公开的和本租户的
        query.$or = [
          { visibility: 'public' },
          { tenant_id: user.tenant_id },
          { visibility: 'tenant', tenant_id: user.tenant_id }
        ];
        break;
        
      case 'shop_products':
        // 商品：根据租户订阅计划过滤
        // 这里需要根据具体业务逻辑实现
        break;
        
      default:
        // 默认按租户隔离
        if (user.tenant_id) {
          query.tenant_id = user.tenant_id;
        }
    }
    
    return query;
  }
  
  /**
   * 检查是否为下属关系
   * @param {Object} manager 管理者
   * @param {Object} data 被访问的数据
   * @returns {Boolean}
   */
  isSubordinate(manager, data) {
    // TODO: 实现组织架构关系检查
    // 这里需要根据实际的组织架构来实现
    return false;
  }
  
  /**
   * 批量验证数据访问权限
   * @param {Object} user 用户信息
   * @param {Array} dataList 数据列表
   * @param {String} operation 操作类型
   * @returns {Array} 有权限访问的数据列表
   */
  filterAccessibleData(user, dataList, operation) {
    return dataList.filter(data => {
      try {
        this.validateDataAccess(user, data, operation);
        return true;
      } catch (error) {
        console.warn('数据访问权限验证失败:', error.message);
        return false;
      }
    });
  }
  
  /**
   * 获取数据隔离统计信息
   * @returns {Object} 统计信息
   */
  getIsolationStats() {
    const stats = {
      totalCollections: this.collectionIsolationMap.size,
      isolationLevels: {},
      permissions: Array.from(this.operationPermissions.keys())
    };
    
    // 统计各隔离级别的集合数量
    for (const level of Object.values(ISOLATION_LEVELS)) {
      stats.isolationLevels[level] = 0;
    }
    
    for (const level of this.collectionIsolationMap.values()) {
      stats.isolationLevels[level]++;
    }
    
    return stats;
  }
  
  /**
   * 添加新的集合隔离配置
   * @param {String} collection 集合名称
   * @param {String} isolationLevel 隔离级别
   */
  addCollectionIsolation(collection, isolationLevel) {
    if (!Object.values(ISOLATION_LEVELS).includes(isolationLevel)) {
      throw new Error(`无效的隔离级别: ${isolationLevel}`);
    }
    
    this.collectionIsolationMap.set(collection, isolationLevel);
    console.log(`已添加集合隔离配置: ${collection} -> ${isolationLevel}`);
  }
}

// 创建并导出单例
const dataIsolationManager = new DataIsolationManager();

export default dataIsolationManager;

/**
 * 便捷的数据隔离操作方法
 */
export const DataIsolation = {
  // 创建安全查询
  secureQuery: (user, collection, query) => 
    dataIsolationManager.createSecureQuery(user, collection, query),
    
  // 验证数据访问
  validate: (user, data, operation) => 
    dataIsolationManager.validateDataAccess(user, data, operation),
    
  // 创建安全记录
  secureRecord: (user, collection, data) => 
    dataIsolationManager.createSecureRecord(user, collection, data),
    
  // 更新安全记录
  updateRecord: (user, data) => 
    dataIsolationManager.updateSecureRecord(user, data),
    
  // 过滤可访问数据
  filterData: (user, dataList, operation) => 
    dataIsolationManager.filterAccessibleData(user, dataList, operation),
    
  // 获取统计信息
  stats: () => dataIsolationManager.getIsolationStats()
};

/**
 * 使用示例：
 * 
 * // 创建安全查询条件
 * const secureQuery = DataIsolation.secureQuery(user, 'flocks', {
 *   breed: '白鹅',
 *   health_status: 'healthy'
 * });
 * // 结果会自动添加 tenant_id 过滤
 * 
 * // 验证数据访问权限
 * try {
 *   DataIsolation.validate(user, flockData, 'update');
 *   // 有权限，可以继续操作
 * } catch (error) {
 *   // 无权限，显示错误信息
 * }
 * 
 * // 创建安全的数据记录
 * const secureFlockData = DataIsolation.secureRecord(user, 'flocks', {
 *   flock_name: '鹅群A',
 *   breed: '白鹅',
 *   quantity: 100
 * });
 * // 结果会自动添加 tenant_id, created_by 等字段
 */