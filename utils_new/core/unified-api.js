/**
 * 智慧养鹅SAAS平台统一API客户端
 * 整合了所有API调用功能，支持平台级和租户级数据隔离
 */

import TenantContextManager from './tenant-context.js';

class UnifiedAPIClient {
  constructor() {
    this.init();
  }
  
  init() {
    // 基础配置
    this.baseConfig = {
      timeout: 10000,        // 10秒超时
      retries: 3,           // 重试3次
      cache: true,          // 启用缓存
      cacheExpiry: 60000    // 缓存1分钟
    };
    
    // 缓存和请求队列
    this.cache = new Map();
    this.requestQueue = new Map();
    
    // 初始化微信云环境
    this.initCloudEnvironment();
  }
  
  /**
   * 初始化微信云环境
   */
  initCloudEnvironment() {
    if (typeof wx !== 'undefined' && wx.cloud) {
      wx.cloud.init({
        env: 'your-cloud-env-id', // 替换为实际的云环境ID
        traceUser: true
      });
    }
  }
  
  /**
   * 平台级API调用
   * 用于超级管理员管理全平台功能
   */
  async platform(endpoint, data = {}, options = {}) {
    // 验证平台级权限
    const currentUser = await this.getCurrentUser();
    if (!this.isPlatformAdmin(currentUser)) {
      throw new Error('需要平台管理员权限');
    }
    
    return this.callCloudFunction(`platform-${endpoint}`, data, options);
  }
  
  /**
   * 租户级API调用
   * 自动注入租户ID，确保数据隔离
   */
  async tenant(endpoint, data = {}, options = {}) {
    const tenantContext = TenantContextManager.getCurrentTenant();
    
    // 检查租户上下文
    if (!tenantContext && !TenantContextManager.isPlatformMode()) {
      throw new Error('未设置租户上下文，请先选择租户');
    }
    
    // 注入租户ID
    const tenantData = {
      ...data,
      tenant_id: tenantContext?.tenant_id
    };
    
    return this.callCloudFunction(`tenant-${endpoint}`, tenantData, options);
  }
  
  /**
   * 认证相关API调用
   */
  async auth(endpoint, data = {}, options = {}) {
    return this.callCloudFunction(`auth-${endpoint}`, data, options);
  }
  
  /**
   * 共享功能API调用
   */
  async shared(endpoint, data = {}, options = {}) {
    return this.callCloudFunction(`shared-${endpoint}`, data, options);
  }
  
  /**
   * 核心云函数调用方法
   */
  async callCloudFunction(name, data, options = {}) {
    const config = { ...this.baseConfig, ...options };
    const cacheKey = this.generateCacheKey(name, data);
    
    // 检查缓存
    if (config.cache && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < config.cacheExpiry) {
        console.log(`API缓存命中: ${name}`);
        return cached.data;
      } else {
        this.cache.delete(cacheKey);
      }
    }
    
    // 防重复请求
    if (this.requestQueue.has(cacheKey)) {
      console.log(`等待重复请求: ${name}`);
      return this.requestQueue.get(cacheKey);
    }
    
    // 创建请求Promise
    const requestPromise = this.executeRequest(name, data, config);
    this.requestQueue.set(cacheKey, requestPromise);
    
    try {
      const result = await requestPromise;
      
      // 缓存成功的结果
      if (config.cache && result.success) {
        this.cache.set(cacheKey, {
          data: result,
          timestamp: Date.now()
        });
      }
      
      return result;
    } finally {
      // 清理请求队列
      this.requestQueue.delete(cacheKey);
    }
  }
  
  /**
   * 执行请求（包含重试逻辑）
   */
  async executeRequest(name, data, config) {
    let lastError;
    
    for (let attempt = 0; attempt <= config.retries; attempt++) {
      try {
        console.log(`调用云函数: ${name} (尝试 ${attempt + 1}/${config.retries + 1})`);
        
        const result = await wx.cloud.callFunction({
          name,
          data: {
            ...data,
            timestamp: Date.now(),
            request_id: this.generateRequestId(),
            client_info: this.getClientInfo()
          }
        });
        
        return this.handleResponse(result);
      } catch (error) {
        lastError = error;
        console.warn(`云函数调用失败: ${name} (${attempt + 1}/${config.retries + 1})`, error);
        
        // 如果还有重试机会，等待后重试
        if (attempt < config.retries) {
          const delay = Math.pow(2, attempt) * 1000; // 指数退避：1s, 2s, 4s
          await this.delay(delay);
        }
      }
    }
    
    throw this.handleError(lastError, name);
  }
  
  /**
   * 处理响应数据
   */
  handleResponse(result) {
    if (result.result && result.result.success) {
      return {
        success: true,
        data: result.result.data,
        message: result.result.message,
        timestamp: result.result.timestamp
      };
    } else {
      const errorMessage = result.result?.message || '请求失败';
      throw new Error(errorMessage);
    }
  }
  
  /**
   * 处理错误
   */
  handleError(error, functionName) {
    console.error(`云函数调用最终失败: ${functionName}`, error);
    
    return {
      success: false,
      message: error.message || '网络请求失败',
      code: error.errCode || 'UNKNOWN_ERROR',
      details: error
    };
  }
  
  /**
   * 获取当前用户信息
   */
  async getCurrentUser() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        return userInfo;
      }
      
      // 如果本地没有用户信息，尝试从云端获取
      const result = await this.auth('getUserInfo');
      if (result.success) {
        wx.setStorageSync('userInfo', result.data);
        return result.data;
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
    
    return null;
  }
  
  /**
   * 检查是否为平台管理员
   */
  isPlatformAdmin(user) {
    if (!user || !user.role) return false;
    
    const platformRoles = ['SUPER_ADMIN', 'PLATFORM_ADMIN'];
    return platformRoles.includes(user.role);
  }
  
  /**
   * 生成缓存键
   */
  generateCacheKey(name, data) {
    const dataStr = JSON.stringify(data, Object.keys(data).sort());
    return `${name}_${this.hashCode(dataStr)}`;
  }
  
  /**
   * 生成请求ID
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 获取客户端信息
   */
  getClientInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      return {
        platform: systemInfo.platform,
        version: systemInfo.version,
        brand: systemInfo.brand,
        model: systemInfo.model
      };
    } catch (error) {
      return { platform: 'unknown' };
    }
  }
  
  /**
   * 延时函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * 简单哈希函数
   */
  hashCode(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  }
  
  /**
   * 清理缓存
   */
  clearCache() {
    this.cache.clear();
    console.log('API缓存已清理');
  }
  
  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// 创建并导出单例
const unifiedAPI = new UnifiedAPIClient();

export default unifiedAPI;

/**
 * 便捷的API调用方法
 */
export const API = {
  // 平台级API
  platform: (endpoint, data, options) => unifiedAPI.platform(endpoint, data, options),
  
  // 租户级API  
  tenant: (endpoint, data, options) => unifiedAPI.tenant(endpoint, data, options),
  
  // 认证API
  auth: (endpoint, data, options) => unifiedAPI.auth(endpoint, data, options),
  
  // 共享功能API
  shared: (endpoint, data, options) => unifiedAPI.shared(endpoint, data, options)
};

/**
 * 使用示例：
 * 
 * // 平台级调用
 * const result = await API.platform('tenantManagement', {
 *   action: 'list'
 * });
 * 
 * // 租户级调用
 * const flocks = await API.tenant('flockManagement', {
 *   action: 'getFlockList'
 * });
 * 
 * // 认证调用
 * const userInfo = await API.auth('getUserInfo');
 */