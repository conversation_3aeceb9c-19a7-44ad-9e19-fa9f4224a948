/**
 * 为空页面添加样式引用脚本
 */

const fs = require('fs');
const path = require('path');

class EmptyPageStyleAdder {
  constructor() {
    this.rootPath = process.cwd();
    this.emptyPages = [
      'pages/production-detail/record-list/record-list.wxss',
      'pages/production-detail/report/report.wxss',
      'pages/production-modules/finance/finance.wxss',
      'pages/shop-detail/cart/cart.wxss',
      'pages/shop-detail/checkout/checkout.wxss',
      'pages/shop-detail/goods/add/add.wxss',
      'pages/shop-detail/order-success/order-success.wxss',
      'pages/workspace/purchase/approve/approve.wxss'
    ];
  }

  /**
   * 执行添加样式
   */
  async addStyles() {
    console.log('🎨 开始为空页面添加样式引用...');
    
    for (const stylePath of this.emptyPages) {
      await this.addStyleImport(stylePath);
    }
    
    console.log('✅ 空页面样式添加完成！');
  }

  /**
   * 为单个样式文件添加引用
   */
  async addStyleImport(stylePath) {
    const fullPath = path.join(this.rootPath, stylePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️ 文件不存在: ${stylePath}`);
      return;
    }

    try {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // 检查是否已经有样式引用
      if (!content.includes('empty-page-common.wxss')) {
        const relativePath = this.getRelativePath(stylePath);
        const newContent = content + `\n@import "${relativePath}/styles/empty-page-common.wxss";`;
        fs.writeFileSync(fullPath, newContent);
        console.log(`🎨 添加样式: ${stylePath}`);
      }
    } catch (error) {
      console.error(`❌ 添加样式失败: ${stylePath} - ${error.message}`);
    }
  }

  /**
   * 获取相对路径
   */
  getRelativePath(stylePath) {
    const depth = stylePath.split('/').length - 1;
    return '../'.repeat(depth - 1);
  }
}

// 执行添加样式
if (require.main === module) {
  const adder = new EmptyPageStyleAdder();
  adder.addStyles().catch(console.error);
}

module.exports = EmptyPageStyleAdder;
