/**
 * 清理第三方组件引用脚本
 * 移除对不存在的第三方组件库的引用
 */

const fs = require('fs');
const path = require('path');

class ThirdPartyComponentCleaner {
  constructor() {
    this.rootPath = process.cwd();
    this.cleanedFiles = [];
    this.errors = [];
  }

  /**
   * 执行清理
   */
  async clean() {
    console.log('🧹 开始清理第三方组件引用...');
    
    // 1. 扫描所有页面JSON文件
    const jsonFiles = this.findPageJsonFiles();
    
    // 2. 清理第三方组件引用
    for (const jsonFile of jsonFiles) {
      await this.cleanJsonFile(jsonFile);
    }
    
    // 3. 生成清理报告
    this.generateReport();
    
    console.log('✅ 第三方组件引用清理完成！');
  }

  /**
   * 清理单个JSON文件
   */
  async cleanJsonFile(jsonFile) {
    try {
      const content = fs.readFileSync(jsonFile, 'utf8');
      const config = JSON.parse(content);
      
      if (config.usingComponents) {
        let modified = false;
        const cleanedComponents = {};
        
        for (const [componentName, componentPath] of Object.entries(config.usingComponents)) {
          // 检查是否是第三方组件
          if (this.isThirdPartyComponent(componentPath)) {
            console.log(`🗑️ 移除第三方组件: ${componentName} (${componentPath}) from ${path.relative(this.rootPath, jsonFile)}`);
            modified = true;
          } else {
            cleanedComponents[componentName] = componentPath;
          }
        }
        
        if (modified) {
          config.usingComponents = cleanedComponents;
          fs.writeFileSync(jsonFile, JSON.stringify(config, null, 2));
          this.cleanedFiles.push(`✅ 清理文件: ${path.relative(this.rootPath, jsonFile)}`);
        }
      }
    } catch (error) {
      this.errors.push(`❌ 处理文件失败: ${jsonFile} - ${error.message}`);
    }
  }

  /**
   * 判断是否是第三方组件
   */
  isThirdPartyComponent(componentPath) {
    const thirdPartyPatterns = [
      '@vant/weapp',
      'vant-weapp',
      'tdesign-miniprogram',
      'weui-miniprogram',
      'miniprogram-api-promise',
      'wx-promise-pro'
    ];
    
    return thirdPartyPatterns.some(pattern => componentPath.includes(pattern));
  }

  /**
   * 查找所有页面JSON文件
   */
  findPageJsonFiles() {
    const jsonFiles = [];
    const pagesDir = path.join(this.rootPath, 'pages');
    
    if (fs.existsSync(pagesDir)) {
      this.scanDirectory(pagesDir, jsonFiles, '.json');
    }
    
    return jsonFiles;
  }

  /**
   * 递归扫描目录
   */
  scanDirectory(dir, files, extension) {
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          this.scanDirectory(fullPath, files, extension);
        } else if (item.endsWith(extension)) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`警告: 无法扫描目录 ${dir}: ${error.message}`);
    }
  }

  /**
   * 生成清理报告
   */
  generateReport() {
    const report = `# 第三方组件清理报告

## 📊 清理统计
- 清理文件数: ${this.cleanedFiles.length}个
- 发现错误: ${this.errors.length}个

## ✅ 已清理的文件
${this.cleanedFiles.map(file => `- ${file}`).join('\n')}

## ❌ 处理错误
${this.errors.map(error => `- ${error}`).join('\n')}

## 📝 说明
本次清理移除了以下类型的第三方组件引用：
- @vant/weapp 组件
- vant-weapp 组件  
- tdesign-miniprogram 组件
- weui-miniprogram 组件
- 其他第三方组件库

这些组件库需要通过 npm 安装并构建后才能使用。
如果需要使用这些组件，请：
1. 在项目根目录创建 package.json
2. 安装相应的组件库
3. 在微信开发者工具中构建 npm

---
清理时间: ${new Date().toLocaleString()}
`;

    fs.writeFileSync(path.join(this.rootPath, 'docs/third-party-component-cleanup-report.md'), report);
    console.log('📄 清理报告已保存到: docs/third-party-component-cleanup-report.md');
  }
}

// 执行清理
if (require.main === module) {
  const cleaner = new ThirdPartyComponentCleaner();
  cleaner.clean().catch(console.error);
}

module.exports = ThirdPartyComponentCleaner;
