/**
 * 修复空模板文件脚本
 * 为空的WXML文件添加基本的页面结构
 */

const fs = require('fs');
const path = require('path');

class EmptyTemplateFixer {
  constructor() {
    this.rootPath = process.cwd();
    this.fixedFiles = [];
    this.emptyTemplates = [
      'pages/production-detail/ai-diagnosis/ai-diagnosis.wxml',
      'pages/production-detail/record-list/record-list.wxml', 
      'pages/production-detail/report/report.wxml',
      'pages/production-modules/finance/finance.wxml',
      'pages/shop-detail/cart/cart.wxml',
      'pages/shop-detail/checkout/checkout.wxml',
      'pages/shop-detail/goods/add/add.wxml',
      'pages/shop-detail/order-success/order-success.wxml',
      'pages/workspace/purchase/approve/approve.wxml'
    ];
  }

  /**
   * 执行修复
   */
  async fix() {
    console.log('🔧 开始修复空模板文件...');
    
    for (const templatePath of this.emptyTemplates) {
      await this.fixTemplate(templatePath);
    }
    
    this.generateReport();
    console.log('✅ 空模板文件修复完成！');
  }

  /**
   * 修复单个模板文件
   */
  async fixTemplate(templatePath) {
    const fullPath = path.join(this.rootPath, templatePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️ 文件不存在: ${templatePath}`);
      return;
    }

    try {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // 检查是否是空模板
      if (this.isEmptyTemplate(content)) {
        const newContent = this.generatePageContent(templatePath);
        fs.writeFileSync(fullPath, newContent);
        this.fixedFiles.push(`✅ 修复空模板: ${templatePath}`);
        console.log(`🔧 修复: ${templatePath}`);
      }
    } catch (error) {
      console.error(`❌ 修复失败: ${templatePath} - ${error.message}`);
    }
  }

  /**
   * 判断是否是空模板
   */
  isEmptyTemplate(content) {
    const lines = content.trim().split('\n');
    return lines.length <= 2 && lines.some(line => line.includes('.wxml'));
  }

  /**
   * 生成页面内容
   */
  generatePageContent(templatePath) {
    const pageName = this.getPageName(templatePath);
    const pageTitle = this.getPageTitle(templatePath);
    
    return `<!--${templatePath}-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">${pageTitle}</text>
  </view>

  <!-- 页面内容 -->
  <view class="content">
    <view class="empty-state">
      <text class="empty-text">页面开发中...</text>
      <text class="empty-desc">该功能正在开发中，敬请期待</text>
    </view>
  </view>
</view>`;
  }

  /**
   * 获取页面名称
   */
  getPageName(templatePath) {
    const parts = templatePath.split('/');
    return parts[parts.length - 2];
  }

  /**
   * 获取页面标题
   */
  getPageTitle(templatePath) {
    const titleMap = {
      'ai-diagnosis': 'AI诊断',
      'record-list': '记录列表',
      'report': '报告',
      'finance': '财务管理',
      'cart': '购物车',
      'checkout': '结算',
      'add': '添加商品',
      'order-success': '订单成功',
      'approve': '审批'
    };
    
    const pageName = this.getPageName(templatePath);
    return titleMap[pageName] || pageName;
  }

  /**
   * 生成修复报告
   */
  generateReport() {
    const report = `# 空模板文件修复报告

## 📊 修复统计
- 修复文件数: ${this.fixedFiles.length}个
- 检查文件数: ${this.emptyTemplates.length}个

## ✅ 已修复的文件
${this.fixedFiles.map(file => `- ${file}`).join('\n')}

## 📝 修复内容
为每个空模板文件添加了：
1. 基本的页面结构
2. 页面标题
3. 开发中提示
4. 统一的样式类名

## 🎯 后续建议
1. 为每个页面添加具体的功能实现
2. 完善页面样式
3. 添加数据交互逻辑
4. 进行功能测试

---
修复时间: ${new Date().toLocaleString()}
`;

    fs.writeFileSync(path.join(this.rootPath, 'docs/empty-template-fix-report.md'), report);
    console.log('📄 修复报告已保存到: docs/empty-template-fix-report.md');
  }
}

// 执行修复
if (require.main === module) {
  const fixer = new EmptyTemplateFixer();
  fixer.fix().catch(console.error);
}

module.exports = EmptyTemplateFixer;
