# 项目目录重构计划

## 当前目录结构问题分析

1. **页面组织混乱**：平台级和租户级页面混在一起
2. **组件分类不清**：业务组件和通用组件没有明确区分
3. **功能边界模糊**：缺少清晰的SAAS架构层次

## 新目录结构设计

### 1. 页面目录重构（pages/）

#### 当前结构 → 新结构映射

```
当前混乱结构：
pages/
├── shop-detail/          → pages/shared/shop/
├── ai-config/            → pages/platform/ai-config/
├── home/                 → pages/common/dashboard/
├── auth/                 → pages/common/auth/
├── workspace/            → pages/shared/workspace/
└── ...

新SAAS结构：
pages/
├── platform/             # 平台级管理页面（超级管理员）
│   ├── dashboard/        # 平台仪表板
│   ├── goose-price/      # 今日鹅价管理
│   ├── announcements/    # 平台公告管理
│   ├── knowledge-base/   # 知识库管理
│   ├── shop-management/  # 商城模块管理
│   ├── tenant-management/# 租户管理
│   ├── ai-config/        # AI大模型配置
│   └── system-settings/  # 系统设置
├── tenant/               # 租户级管理页面
│   ├── dashboard/        # 租户仪表板
│   ├── flock-management/ # 鹅群管理
│   ├── material-management/ # 生产物料管理
│   ├── health-records/   # 健康记录管理
│   └── financial-management/ # 财务管理
├── shared/               # 共享功能页面
│   ├── shop/            # 商城系统
│   └── workspace/       # OA工作空间
└── common/               # 通用页面
    ├── auth/            # 认证相关
    ├── profile/         # 个人中心
    └── settings/        # 通用设置
```

### 2. 组件目录重构（components/）

```
当前混乱结构：
components/
├── record-detail-modal/
├── environment-data-item/
├── health-trend-chart/
├── goose-price/
└── ...

新分层结构：
components/
├── platform/             # 平台级组件
│   ├── tenant-selector/  # 租户选择器
│   ├── global-stats/     # 全局统计
│   ├── platform-nav/     # 平台导航
│   └── admin-tools/      # 管理工具
├── tenant/               # 租户级组件
│   ├── flock-card/       # 鹅群卡片
│   ├── health-monitor/   # 健康监控
│   ├── finance-chart/    # 财务图表
│   └── material-tracker/ # 物料追踪
├── shared/               # 共享业务组件
│   ├── shop-cart/        # 购物车
│   ├── order-manager/    # 订单管理
│   └── workspace-tools/  # 工作空间工具
└── common/               # 通用基础组件
    ├── data-table/       # 数据表格
    ├── form-builder/     # 表单构建器
    ├── chart/           # 图表组件
    └── ui-elements/     # UI元素
```

### 3. 工具函数重构（utils/）

```
当前重复结构：
utils/
├── api.js
├── api-client-unified.js
├── api-client-final.js
├── optimized-api-client.js
└── ...

新统一结构：
utils/
├── core/                 # 核心工具
│   ├── unified-api.js   # 统一API客户端
│   ├── data-isolation.js # 数据隔离
│   ├── permission-manager.js # 权限管理
│   └── tenant-context.js # 租户上下文
├── business/             # 业务工具
│   ├── flock-calculator.js # 鹅群计算
│   ├── health-analyzer.js  # 健康分析
│   └── finance-calculator.js # 财务计算
└── helpers/              # 辅助工具
    ├── format.js         # 格式化
    ├── validation.js     # 验证
    └── storage.js        # 存储管理
```

## 重构步骤

### 第一步：创建新目录结构（不破坏现有功能）
### 第二步：迁移页面文件到新位置
### 第三步：更新所有引用路径
### 第四步：删除旧目录结构

## 注意事项

1. **渐进式重构**：不影响现有功能的正常运行
2. **路径更新**：同步更新app.json中的页面路径配置
3. **组件引用**：更新所有组件的引用路径
4. **测试验证**：每步重构后进行功能测试